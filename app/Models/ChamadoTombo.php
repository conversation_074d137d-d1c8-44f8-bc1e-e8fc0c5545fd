<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;

class ChamadoTombo extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'chamado_tombos';

    protected $fillable = [
        'chamado_id',
        'nm_tombo',
    ];

    #region Relacionamentos

    public function chamado(): Relation
    {
        return $this->belongsTo(Chamado::class, 'chamado_id', 'id_chamado');
    }

    #endregion
}
