<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Servico extends Model
{
    use HasFactory;
    protected $connection = 'pgsql';
    protected $table = 'servicos';
    public $timestamps = true;

    protected $fillable = [
        'id',
        'nome',
        'descricao',
        'setor_responsavel',
        'status',
        'criticidade'
    ];

    #region Relacionamentos

    public function setor(): Relation
    {
        return $this->belongsTo(HabilitarSetor::class, 'setor_responsavel', 'setor_id');
    }
    public function chamados(): Relation
    {
        return $this->hasMany(Chamado::class, 'servico_id', 'id');
    }

    #endRegion

    #region Scopes

    public function scopeServicoSetores(Builder $query, $ids): Builder
    {
        return $query->join('habilitar_setores', 'servicos.setor_responsavel', '=', 'habilitar_setores.setor_id')
                    ->whereIn('servicos.setor_responsavel', $ids)
                    ->where('servicos.status', true)
                    ->where('habilitar_setores.habilitado', true)
                    ->select('servicos.id', 'servicos.nome');
    }

    #endRegion
}
