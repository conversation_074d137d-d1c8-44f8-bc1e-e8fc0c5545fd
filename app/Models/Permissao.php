<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletes;

class Permissao extends Model
{   
    protected $table = 'permissoes';
    public $timestamps = true;

    protected $fillable = [
        'id',
        'codigo',
        'permissao'
    ];

    #region Relacionamentos

    public function perfis(): Relation
    {
        return $this->belongsToMany(Perfil::class);
    }

    #endregion

}
