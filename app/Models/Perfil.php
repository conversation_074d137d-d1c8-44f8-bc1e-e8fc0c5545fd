<?php

namespace App\Models;

use App\Models\SGS\Usuario;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletes;

class Perfil extends Model
{
    use SoftDeletes;

    protected $connection = 'pgsql';

    protected $table = 'perfis';

    public $timestamps = true;

    protected $with = "permissoes";

    protected $fillable = [
        'id',
        'nome',
        'descricao'
    ];

    #region Relacionamentos

    public function permissoes(): Relation
    {
        return $this->belongsToMany(Permissao::class)->withTimestamps();
    }

    public function usuarios()
    {
        return $this->belongsToMany(Usuario::class);   
    }

    #endregion
}
