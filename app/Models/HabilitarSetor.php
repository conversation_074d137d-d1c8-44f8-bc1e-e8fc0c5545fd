<?php

namespace App\Models;

use App\Models\SGS\Setor;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletes;

class HabilitarSetor extends Model
{
    use SoftDeletes, HasFactory;

    protected $connection = 'pgsql';
    protected $fillable = ['setor_id', 'usuario_id', 'habilitado', 'caminho_logomarca'];

    protected $table = 'habilitar_setores';

    protected $primaryKey = 'id';

    #region Relacionamentos

    public function setor(): Relation
    {
        return $this->belongsTo(Setor::class, 'setor_id', 'id_setor');
    }

    public function servicos(): Relation
    {
        return $this->hasMany(Servico::class, 'setor_responsavel', 'setor_id');
    }

    #endregion
}
