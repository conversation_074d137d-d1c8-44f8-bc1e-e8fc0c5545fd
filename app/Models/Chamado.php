<?php

namespace App\Models;

use App\Models\SGS\Setor;
use App\Models\SGS\Usuario;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;

class Chamado extends Model
{
    use HasFactory;
    protected $connection = 'pgsql';
    protected $table = 'chamados';
    protected $primaryKey = 'id_chamado';

    protected $fillable = [
        'servico_id',
        'titulo',
        'descricao',
        'usuario_solicitante_id',
        'setor_atendente_id',
        'setor_solicitante_id',
        'telefone_contato',
        'caminho_arquivo',
        'status',
        'concluded_at',
        'usuario_atendente_id'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'concluded_at'
    ];

    #region Relacionamentos

    public function usuarioSolicitante(): Relation
    {
        return $this->belongsTo(Usuario::class, 'usuario_solicitante_id', 'id_usuario');
    }
    public function usuarioAtendente(): Relation
    {
        return $this->belongsTo(Usuario::class, 'usuario_atendente_id', 'id_usuario');
    }

    public function servico(): Relation
    {
        return $this->belongsTo(Servico::class, 'servico_id', 'id');
    }

    public function setorAtendente(): Relation
    {
        return $this->belongsTo(Setor::class, 'setor_atendente_id', 'id_setor');
    }
    public function setorSolicitante(): Relation
    {
        return $this->belongsTo(Setor::class, 'setor_solicitante_id', 'id_setor');
    }

    public function tombos(): Relation
    {
        return $this->hasMany(ChamadoTombo::class, 'chamado_id', 'id_chamado');
    }

    public function mensagens(): Relation
    {
        return $this->hasMany(ChamadoMensagem::class, 'chamado_id', 'id_chamado');
    }

    #endregion

    

}
