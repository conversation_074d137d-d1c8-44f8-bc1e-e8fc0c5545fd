<?php

namespace App\Models;

use App\Models\SGS\Setor;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class HabilitarUnidadeOrganizacional extends Model
{
    use SoftDeletes, HasFactory;

    protected $connection = 'pgsql';

    protected $fillable = [
        'unidade_id',
        'habilitado',
        'descricao',
        'caminho_logomarca',
    ];

    protected $table = 'habilitar_unidades_organizacionais';

    protected $primaryKey = 'id';

    #region Relacionementos
    public function setor(): BelongsTo
    {
        return $this->belongsTo(Setor::class, 'unidade_id', 'id_setor');
    }
    #endregion

}
