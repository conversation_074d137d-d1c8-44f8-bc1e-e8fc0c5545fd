<?php

namespace App\Models;

use App\Models\SGS\Usuario;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;

class ChamadoMensagem extends Model
{
    use HasFactory;
    protected $connection = 'pgsql';
    protected $table = 'chamado_mensagens';
    protected $primaryKey = 'id';
    // Toda vez que uma mensagem for criada ou atualizada, o campo updated_at do chamado será atualizado
    protected $touches = ['chamado'];

    protected $fillable = [
        'chamado_id',
        'mensagem',
        'usuario_id',
        'tipo_mensagem',
        'tipo_usuario',
        'caminho_arquivo',
    ];

    #region Relacionamentos
    public function chamado(): Relation
    {
        return $this->belongsTo(Chamado::class, 'chamado_id', 'id_chamado');
    }

    public function usuario(): Relation
    {
        return $this->belongsTo(Usuario::class, 'usuario_id', 'id_usuario');
    }
    #endregion
}
