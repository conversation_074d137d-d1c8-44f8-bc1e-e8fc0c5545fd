<?php

namespace App\Models\SGS;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Campus extends Model
{
    use HasFactory;

    protected $connection = 'pgsql_sgs';

    protected $table = 'campi';

    protected $primaryKey = 'id_campus';

    #region Relacionamentos

    public function setores()
    {
        return $this->hasMany(Setor::class, 'id_campus', 'id_campus');
    }

    #endregion
}
