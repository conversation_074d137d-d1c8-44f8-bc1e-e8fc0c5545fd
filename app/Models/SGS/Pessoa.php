<?php

namespace App\Models\SGS;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;

class Pessoa extends Model
{
    protected $connection = 'pgsql_sgs';

    protected $table = 'pessoas';

    protected $primaryKey = 'ds_cpf';

    protected $keyType = 'string';

    #region Relacionamentos

    public function usuario(): Relation
    {
        return $this->belongsTo(Usuario::class, 'ds_cpf', 'ds_cpf');
    }

    public function lotacao()
    {
        return $this->hasOne(Lotacao::class, 'ds_cpf', 'ds_cpf');
    }

    #endregion

    public function getNomeAttribute()
    {
        $nome_array = explode(' ', ucwords(strtolower($this->attributes['ds_nomepessoa'])));
        $primeiro_nome = $nome_array[0];
        $ultimo_nome = last($nome_array);

        return "$primeiro_nome $ultimo_nome";
    }
}
