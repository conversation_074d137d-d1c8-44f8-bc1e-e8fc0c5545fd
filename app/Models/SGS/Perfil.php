<?php

namespace App\Models\SGS;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;

class Perfil extends Model
{
    use HasFactory;

    protected $connection = 'pgsql_sgs';

    protected $table = 'perfis';

    protected $primaryKey = 'id_perfil';

    public function usuarios(): Relation
    {
        return $this->belongsToMany(Usuario::class, 'perfisusuarios', 'id_perfil', 'id_usuario');
    }

    public function atividades()
    {
        return $this->belongsToMany(Atividade::class, 'permissoesatividadesperfis', 'id_perfil', 'id_atividade');
    }
}
