<?php

namespace App\Models\SGS;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;

class Lotacao extends Model
{
    use HasFactory;

    protected $connection = 'pgsql_sgs';

    protected $table = 'lotacao';

    protected $primaryKey = 'id_lotacao';

    #region Relacionamentos

    public function exercicio(): Relation
    {
        return $this->hasOne(Setor::class, 'id_setor', 'id_setor_exercicio');
    }

    public function setor(): Relation
    {
        return $this->hasOne(Setor::class, 'id_setor', 'id_setor_lotacao');
    }

    #endregion
}
