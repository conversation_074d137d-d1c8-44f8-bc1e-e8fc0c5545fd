<?php

namespace App\Models\SGS;

use App\Models\Perfil;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Facades\DB;

class Usuario extends Authenticatable
{
    protected $connection = 'pgsql_sgs';

    protected $table = 'usuarios';

    protected $primaryKey = 'id_usuario';

    protected $with = ['lotacao', 'setor', 'unidade_exercicio', 'perfis'];

    protected $usuarios_ds_cpf = 'usuarios.ds_cpf';

    protected $pessoascategoria_ds_categoria = 'pessoascategoria.ds_categoria';

    //region Relacionamentos

    public function pessoa(): Relation
    {
        return $this->hasOne(Pessoa::class, 'ds_cpf', 'ds_cpf');
    }

    public function lotacao(): Relation
    {
        return $this->hasOne(Lotacao::class, 'ds_cpf', 'ds_cpf');
    }

    public function perfis()
    {
        return $this->setConnection('pgsql')->belongsToMany(Perfil::class, 'perfil_usuario', 'id_usuario', 'perfil_id');
    }

    public function setor(): HasOneThrough
    {
        return $this->hasOneThrough(Setor::class, Lotacao::class, 'ds_cpf', 'id_setor', 'ds_cpf', 'id_setor_lotacao');
    }

    public function unidade_exercicio(): HasOneThrough
    {
        return $this->hasOneThrough(Setor::class, Lotacao::class, 'ds_cpf', 'id_setor', 'ds_cpf', 'id_setor_exercicio');
    }

    public function pessoaCategoria(): Relation
    {
        return $this->hasOne(PessoaCategoria::class, 'ds_cpf', 'ds_cpf');
    }

    //endregion

    //region Scopes
    public function scopeWithCategoria(Builder $query): Builder
    {
        return $query->join('pessoascategoria', $this->usuarios_ds_cpf, '=', 'pessoascategoria.ds_cpf')
            ->join('pessoas', $this->usuarios_ds_cpf, '=', 'pessoas.ds_cpf')
            ->select('usuarios.id_usuario', 'usuarios.ds_cpf', $this->pessoascategoria_ds_categoria, 'pessoas.ds_nomepessoa')
            ->whereNotNull($this->pessoascategoria_ds_categoria)
            ->where($this->pessoascategoria_ds_categoria, '<>', '');
    }

    public function scopeFilterByCpf(Builder $query, $cpf): Builder
    {
        return $query->where($this->usuarios_ds_cpf, 'LIKE', "%$cpf%");
    }

    public function scopeFilterByNome(Builder $query, $nome): Builder
    {
        return $query->whereHas('pessoa', function ($q) use ($nome) {
            $q->where('ds_nomepessoa', 'ILIKE', "%$nome%");
        });
    }

    public function scopeFilterBySetor(Builder $query, $setor): Builder
    {
        return $query->whereHas('setor', function ($q) use ($setor) {
            $q->where('sg_setor', 'ILIKE', "%$setor%");
        });
    }

    public function scopeFilterByCategoria(Builder $query, $categoria): Builder
    {
        return $query->where($this->pessoascategoria_ds_categoria, 'LIKE', "%$categoria%");
    }

    public function scopeFilterByPerfil(Builder $query, $perfilId): Builder
    {
        // Obter IDs de usuários que possuem o perfil desejado na conexão alternativa
        $usuarioIds = DB::connection('pgsql')
            ->table('perfil_usuario')
            ->where('perfil_id', $perfilId)
            ->pluck('id_usuario'); // Obtém os IDs de usuários associados ao perfil

        // Filtrar os usuários pelo ID encontrado
        return $query->whereIn('id_usuario', $usuarioIds);
    }

    public function scopeApplyFilters(Builder $query, array $filters): Builder
    {
        $map = [
            'cpf' => 'filterByCpf',
            'nome' => 'filterByNome',
            'setor' => 'filterBySetor',
            'categoria' => 'filterByCategoria',
            'perfil' => 'filterByPerfil',
        ];
        
        foreach ($map as $key => $scopeMethod){
            if(!empty($filters[$key])){
                $query->$scopeMethod($filters[$key]);
            }
        }

        if(!empty($filters['search'])){
            is_numeric($filters['search']) 
            ? $query->filterByCpf($filters['search']) 
            : $query->filterByNome($filters['search']);
        }

        return $query->orderBy('ds_nomepessoa', $filters['sortOrder'] ?? 'asc');
    }
    //endregion

    public function getCategoriaFormatadaAttribute()
    {
        $categoriasMap = [
            'A' => 'Aluno',
            'D' => 'Docente',
            'L' => 'Docente (parcial)',
            'E' => 'Estagiário',
            'P' => 'Prestador',
            'T' => 'Técnico',
            'B' => 'Técnico (parcial)',
            'V' => 'Visitante',
            'O' => 'Outro Servidor',
            'R' => 'Outro Servidor (parcial)',
        ];

        $categorias = str_split($this->ds_categoria);
        $categoriasFormatadas = array_map(function ($categoria) use ($categoriasMap) {
            return $categoriasMap[$categoria];
        }, $categorias);

        return implode(', ', $categoriasFormatadas);
    }

    public function getPerfilFormatadoAttribute()
    {
        $perfisNomes = $this->perfis->pluck('nome')->toArray();

        return implode(', ', $perfisNomes);
    }

    public function PermissoesUsuarioLogado( $tipo_permissao = 'descritas'): ?array
    {
        
        $permissoes = $this->perfis()
                      ->with('permissoes',
                          function ($query)  use ($tipo_permissao) {
                            
                              $query->select('descricao','descricao_menu', 'classe_menu', 'ordem_menu', 'rota_menu', 'rota_adicional_menu')
                              
                                ->when($tipo_permissao == 'descritas', function ($query) {
                                    
                                    return $query->where('descricao_menu', '<>', '');
                                    
                                })

                                ->when($tipo_permissao == 'ocultas', function ($query) use ($tipo_permissao) {
                                    
                                    return $query->whereNull('descricao_menu')
                                    ->orWhere('descricao_menu', '=', '');
                                });
                              
                          })
                      ->get();

        
        $permissoes = $permissoes->flatMap(function ($perfil)  {
            
            return $perfil->permissoes->map(function ($permissao)  {
                
                return (object) [
                    'descricao' => $permissao->descricao,
                    'descricao_menu' => $permissao->descricao_menu,
                    'classe_menu' => $permissao->classe_menu,
                    'ordem_menu' => $permissao->ordem_menu,
                    //'rota_menu' => $permissao->rota_menu,
                    //'rota_adicional_menu' => $permissao->rota_adicional_menu,
                   
                    'rota' => $permissao['rota_menu'] ? ($permissao->rota_adicional_menu ? route($permissao->rota_menu, $permissao->rota_adicional_menu) : route($permissao->rota_menu)) : '#'
                ];
                
            });
        })
                    ->unique('descricao')
                    ->sortBy('ordem_menu')
                    ->values()
                    ->toArray();
              
                    
        /*
        $permissoes = DB::select('
        select permissoes.descricao_menu, permissoes.classe_menu, permissoes.ordem_menu, permissoes.rota_menu, permissoes.rota_adicional_menu
        from permissoes
        join
        (
        select perfil_permissao.permissao_id
        from perfil_usuario
        join perfil_permissao on perfil_permissao.perfil_id = perfil_usuario.perfil_id
        where perfil_usuario.id_usuario = ' . $usuario_id . '
        group by perfil_permissao.permissao_id
        ) permissoes_usuarios on permissoes.id = permissoes_usuarios.permissao_id
        where permissoes.descricao_menu is not null
        and permissoes.descricao_menu <> ' . "''" . '
        order by ordem_menu'
        );
        */

        return $permissoes;
    }
}
