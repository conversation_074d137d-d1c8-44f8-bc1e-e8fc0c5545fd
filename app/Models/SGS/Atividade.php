<?php

namespace App\Models\SGS;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Atividade extends Model
{
    use HasFactory;

    protected $connection = 'pgsql_sgs';

    protected $table = 'atividades';

    protected $primaryKey = 'id_atividade';

    public function perfis()
    {
        return $this->belongsToMany(Perfil::class, 'permissoesatividadesperfis', 'id_atividade', 'id_perfil');
    }
}
