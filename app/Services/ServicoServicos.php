<?php 

namespace App\Services;

use Illuminate\Support\Collection;
use App\Models\{Servico,HabilitarUnidadeOrganizacional, HabilitarSetor};
use App\Models\SGS\Setor;
use Illuminate\Support\Facades\DB;

define('NIVEIS_CRITICIDADE', [
    1 => 'Baixa',
    2 => 'Normal',
    3 => 'Importante',
    4 => 'Crítico'
]);

class ServicoServicos {
    public function __construct(private Servico $servicoModel) {

    }

    public function buscarServicosStatus( string $apresentar, $setor) {

        
       $servicos = Servico::query()
       ->when($apresentar === 'habilitados', fn($query) => $query->where('status', 1))
       ->when($apresentar === 'desabilitados', fn($query) => $query->where('status', 0))
       ->when($setor !== 'todos', fn($query) => $query->where('setor_responsavel', $setor))
       ->get();

        return $servicos;

    }

    public function alternarIdNomeSetor($servicos) {

        if ($servicos instanceof Collection) {
            
            foreach ($servicos as $servico) {
                $nomeSetor = DB::connection('pgsql_sgs')
                    ->table('setores')
                    ->where('id_setor', $servico->setor_responsavel)
                    ->value('ds_nomesetor');

                $servico->setor_responsavel_nome = $nomeSetor;
           }
        } else {
            $nomeSetor = DB::connection('pgsql_sgs')
                ->table('setores')
                ->where('id_setor', $servicos->setor_responsavel)
                ->value('ds_nomesetor');

            $servicos->setor_responsavel_nome = $nomeSetor;
        }

    return $servicos;
    }

    public function alternaIdNomeCampus($setores) {
        //troca o ID do campus pelo nome
        foreach($setores as $setor){
            $setor->id_campus = DB::connection('pgsql_sgs')
            ->table('campi')
            ->where('id_campus', $setor->id_campus)
            ->pluck('ds_nomecampus')
            ->first();
        }

        return $setores;
    }

    public function listaCampusDisponiveis($setores) {

         //pegando o nome do campus dos setores habilitados
         $campusSetoresHabilitados =[];
         foreach($setores as $setor){
             $campusNome = DB::connection('pgsql_sgs')
             ->table('campi')
             ->where('id_campus', $setor->id_campus)
             ->pluck('ds_nomecampus')
             ->first();
             
            
             $campusSetoresHabilitados[] = $campusNome;
 
         }
         $campusSetoresHabilitados = array_unique($campusSetoresHabilitados);

        return $campusSetoresHabilitados;
    }

    public function alternarValorNomeCriticidade($servicos) {
        
        foreach($servicos as $servico){
            $servico->criticidade = NIVEIS_CRITICIDADE[$servico->criticidade];
          }
          
          return $servicos;
    }


    public function selecionaSetoresHabilitados() {
    
        $setoresHabilitados_lista= HabilitarSetor::where('habilitado', true)->pluck('setor_id');
        //verifica se o usário logado eh o admin
        if(auth()->user()->perfis()->first()->id === 1){
            $unidadesHabilitadas_lista = HabilitarUnidadeOrganizacional::where('habilitado', true)->pluck('unidade_id');
            
        }else{
            $unidadesHabilitadas_lista = auth()->user()->setor()->pluck('id_setor');
        }
        
        $setores = Setor::whereIn('id_setor', $setoresHabilitados_lista)
        ->whereIn('id_setorsuperior', $unidadesHabilitadas_lista)
        ->get();

        // dd($setores,$unidadesHabilitadas_lista,auth()->user()->setor()->pluck('id_setor'),$setoresHabilitados_lista);
        return $setores;
    }
    
    public function ListaSetoresDosServicosCriados(string $status = "todos") {
        $servicos = Servico::query()
        ->when($status !== 'todos', fn($query) => $query->where('status', $status === 'habilitados' ? 1 : 0))
        ->get()
        ->unique();

        $servicos =  $this->alternarIdNomeSetor($servicos);
        $setores = [];
        foreach($servicos as $servico ){
            $setores[$servico->setor_responsavel] = $servico->setor_responsavel_nome;
        }
        return $setores;
    }

    public function getSetorResponsavelId(int $servicoId) {
        return $this->servicoModel->where('id', $servicoId)->valueOrFail('setor_responsavel');
    }





}







