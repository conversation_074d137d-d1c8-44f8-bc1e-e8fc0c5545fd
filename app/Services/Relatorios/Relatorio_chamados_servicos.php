<?php

namespace App\Services\Relatorios;

use App\Services\RelatorioServicos;
use App\Services\ChamadoServicos;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;


class Relatorio_chamados_servicos extends RelatorioServicos
{
    
    private const DICIONARIO_FILTROS = [
        "chamados" => [
            "filtro" => [
                "chamado_titulo" => "Título pesquisado",
                "chamado_atendente" => " Setor atendente",
                "chamado_solicitante" => "Setor solicitante",
                "chamado_usuario_solicitante" => "Usuário solicitante",
                "chamado_status" => "Status ",
                "chamado_status_anterior" => "Status ",
                "chamado_direcionamento" => "Direcionamento",
                "chamado_direcionamento_anterior" => "Direcionamento",
                "chamado_servico" => "Serviço"
            ],
            "valores" => [
                "criado_usuario" => "Criado pelo usuário requisitante",
                "direcionado_usuario" => "Direcionados ao usuário requisitante",
                "todos" => "Todos os chamados registrados no sistema",
            ]
        ],
        "servicos" => [
            "filtro" => [
                "servico_status" => "Status do serviço",
                "servico_criticidade" => "Criticidade do serviço",
                "servico_setor" => "Setor responsável"
            ],
            "valores" => [
                "habilitados" => "Serviços habilitados",
                "desabilitados" => "Serviços desabilitados",
                "todos" => "Todos os serviços"
            ]
        ]
    ];

    private const STATUS_POSSIVEIS = ["Aberto", "Em andamento", "Parado", "Concluido"];

    public function __construct( private ChamadoServicos $chamdoServicos)
    {
    }

    public function gerarRelatorioChamados($view, $ordenacao, $filtros)
    {
        // Gerar índices dos filtros
        $indices = $this->gerarIndicesFiltros($filtros);
        
        // Obter dados dos chamados
        $chamados = $this->obterChamadosFiltrados($filtros, $filtros['data_inicio'], $filtros['data_fim']);
        
        // Verificar se existem chamados no período
        if($chamados->count() == 0){
            // Retornar um valor que indique que não há chamados
            return [
                'status' => 'error',
                'message' => 'Não foram encontrados chamados no período especificado.'
            ];
        }
        
        // Gerar estatísticas
        $estatisticasGerais = $this->gerarEstatisticasGerais($chamados);

        $estatisticasEspecificas = $ordenacao == "status" 
        ? $this->gerarEstatisticasPorStatus($chamados)
        : $this->gerarEstatisticasPorPeriodo($chamados);
        
        $dataGeracao = Carbon::now()->format('d/m/Y H:i');
        // Gerar o PDF e retorná-lo
        return $this->gerarVisualizacaoRelatorio($view, compact(
            'indices', 
            'estatisticasGerais', 
            'ordenacao', 
            'estatisticasEspecificas',
            'dataGeracao'
        ));
    }


    private function gerarIndicesFiltros($filtros)
    {
        $indices = [];
        foreach($filtros as $indice => $value) {
            // Verifica se o valor não está vazio e se existe no dicionário de filtros
            if(!empty($value) && isset(self::DICIONARIO_FILTROS["chamados"]["filtro"][$indice])) {
                // Obtém o nome do índice do dicionário
                $nomeIndice = self::DICIONARIO_FILTROS["chamados"]["filtro"][$indice];
                
                // Verifica se existe um valor mapeado no dicionário
                if(isset(self::DICIONARIO_FILTROS["chamados"]["valores"][$value])) {
                    $indices[$nomeIndice] = self::DICIONARIO_FILTROS["chamados"]["valores"][$value];
                } else {
                    // Se não existir um valor mapeado, usa o valor original
                    $indices[$nomeIndice] = $value;
                }
            }
        }
        
        // Adiciona as datas formatadas aos índices
        if (isset($filtros['chamado_abertura']) && !empty($filtros['chamado_abertura'])) {
            $indices['Data de abertura'] = Carbon::parse($filtros['chamado_abertura'])->format('d/m/Y');
        }
        
        if (isset($filtros['data_inicio']) && !empty($filtros['data_inicio'])) {
            $indices['Data inicial'] = Carbon::parse($filtros['data_inicio'])->format('d/m/Y');
            if (isset($filtros['data_fim']) && !empty($filtros['data_fim'])) {
                $indices['Data final'] = Carbon::parse($filtros['data_fim'])->format('d/m/Y');
            } else {
                $indices['Data final'] = Carbon::now()->format('d/m/Y');
            }
        }
        
        return $indices;
    }

    private function obterChamadosFiltrados($filtros, $dataInicio, $dataFim)
    {
        $filtro = $this->chamdoServicos->validaValoresPadrao($filtros);
        $chamados = $this->chamdoServicos->localizarChamadosPeloFiltro($filtro, 'all');
        
       if($dataInicio) {

            $dataInicioObj = \Carbon\Carbon::parse($dataInicio)->startOfDay();
            $chamados = $chamados->filter(function($chamado) use ($dataInicioObj) {
                return $chamado->created_at >= $dataInicioObj;
            });
    
            if ($dataFim) {

                $dataFimObj = \Carbon\Carbon::parse($dataFim)->endOfDay();
                $chamados = $chamados->filter(function($chamado) use ($dataFimObj) {
                    return $chamado->created_at <= $dataFimObj;
                });
            }
        }
        
        return $chamados;
    }


    private function gerarEstatisticasGerais($chamados)
    {
        $totalChamados = $this->chamdoServicos->contaChamadosRegistrados();
        $porcentagem = ($chamados->count() / $totalChamados) * 100;
        
        return [
            "chamados_com_especificacoes" => $chamados->count(),
            "porcentagem" => $porcentagem,
            "periodo_especificado" => (request('data_inicio') || request('data_fim')) ? true : false
        ];
    }


    private function gerarEstatisticasPorStatus($chamados)
    {
        $estatisticasEspecificas = [];
        
        foreach (self::STATUS_POSSIVEIS as $status) {
            $chamadosFiltrados = $chamados->filter(function($chamado) use ($status) {
                return $chamado->status == $status;
            });
            
            $quantidade = $chamadosFiltrados->count();
            $percentual = $chamados->count() > 0 ? ($quantidade / $chamados->count()) * 100 : 0;
            
            $estatisticasEspecificas[$status] = [
                'quantidade' => $quantidade,
                'percentual' => $percentual,
                'chamados' => $this->extrairDetalhesChamados($chamadosFiltrados)
            ];
        }
        
        return $estatisticasEspecificas;
    }

    private function gerarEstatisticasPorPeriodo($chamados)
    {
        $dataInicial = $chamados->min('created_at');
        $dataFinal = $chamados->max('created_at');
        
        if (!$dataInicial || !$dataFinal) {
            return [];
        }
        
        $intervaloEmDias = $dataInicial->diffInDays($dataFinal);
        return $intervaloEmDias <= 60 
            ? $this->agruparChamadosPorSemana($chamados, $dataInicial, $dataFinal)
            : $this->agruparChamadosPorMes($chamados, $dataInicial, $dataFinal);
    }

    private function extrairDetalhesChamados($chamados)
    {
        $detalhesChamados = [];
        foreach ($chamados as $chamado) {
            $detalhesChamados[] = [
                'titulo' => $chamado->titulo,
                'descricao' => $chamado->descricao,
                'data_criacao' => $chamado->created_at->format('d/m/Y'),
                'status' => $chamado->status
            ];
        }
        return $detalhesChamados;
    }

    // AS FUNÇÕES ABAIXO PODEM SER ATÉ CERTO PONTO GENERALIZADAS
    private function agruparChamadosPorSemana($chamados, $dataInicial, $dataFinal)
    {
        $chamadosPorSemana = [];
        
        // Iniciar na segunda-feira da semana da data inicial
        $inicioSemana = $dataInicial->copy()->startOfWeek();
        $fimSemana = $inicioSemana->copy()->endOfWeek();
        
        while ($inicioSemana <= $dataFinal) {
            $periodoLabel = $inicioSemana->format('d/m/Y') . ' a ' . $fimSemana->format('d/m/Y');
            
            // Filtrar chamados desta semana
            $chamadosDaSemana = $chamados->filter(function($chamado) use ($inicioSemana, $fimSemana) {
                return $chamado->created_at >= $inicioSemana && $chamado->created_at <= $fimSemana;
            });
            
            $quantidade = $chamadosDaSemana->count();
            $percentual = $chamados->count() > 0 ? ($quantidade / $chamados->count()) * 100 : 0;
            
            if ($quantidade > 0) {
                $chamadosPorSemana[$periodoLabel] = [
                    'quantidade' => $quantidade,
                    'percentual' => $percentual,
                    'chamados' => $this->extrairDetalhesChamados($chamadosDaSemana)
                ];
            }
            
            // Avançar para a próxima semana
            $inicioSemana->addWeek();
            $fimSemana->addWeek();
        }
        
        return $chamadosPorSemana;
    }

    private function agruparChamadosPorMes($chamados, $dataInicial, $dataFinal)
    {
        $chamadosPorMes = [];
        
        // Iniciar no primeiro dia do mês da data inicial
        $inicioMes = $dataInicial->copy()->startOfMonth();
        $fimMes = $inicioMes->copy()->endOfMonth();
        
        while ($inicioMes <= $dataFinal) {
            $periodoLabel = $inicioMes->format('F/Y'); // Nome do mês/ano
            
            // Filtrar chamados deste mês
            $chamadosDoMes = $chamados->filter(function($chamado) use ($inicioMes, $fimMes) {
                return $chamado->created_at >= $inicioMes && $chamado->created_at <= $fimMes;
            });
            
            $quantidade = $chamadosDoMes->count();
            $percentual = $chamados->count() > 0 ? ($quantidade / $chamados->count()) * 100 : 0;
            
            if ($quantidade > 0) {
                $chamadosPorMes[$periodoLabel] = [
                    'quantidade' => $quantidade,
                    'percentual' => $percentual,
                    'chamados' => $this->extrairDetalhesChamados($chamadosDoMes)
                ];
            }
            
            // Avançar para o próximo mês
            $inicioMes->addMonth();
            $fimMes = $inicioMes->copy()->endOfMonth();
        }
        
        return $chamadosPorMes;
    }

    // Método para obter estatísticas de chamados por serviço
    public function obterEstatisticasChamadosPorServico($servicoId)
    {
        $chamados = DB::table('chamados')
            ->where('servico_id', $servicoId)
            ->get();
            
        $totalChamados = $chamados->count();
        
        // Calcular tempo médio de duração
        $tempoTotal = 0;
        $chamadosConcluidos = 0;
        
        foreach ($chamados as $chamado) {
            if ($chamado->concluded_at) {
                $dataInicio = Carbon::parse($chamado->created_at);
                $dataFim = Carbon::parse($chamado->concluded_at);
                $tempoTotal += $dataInicio->diffInDays($dataFim);
                $chamadosConcluidos++;
            }
        }
        
        $tempoMedio = $chamadosConcluidos > 0 ? round($tempoTotal / $chamadosConcluidos, 1) : 0;
        
        // Obter média de satisfação
        try {
            // Verificar se a tabela existe
            $tabelaExiste = DB::connection('pgsql')->select("SELECT to_regclass('avaliacoes') IS NOT NULL as existe")[0]->existe;
            
            if ($tabelaExiste) {
                $satisfacaoMedia = DB::table('avaliacoes')
                    ->where('servico_id', $servicoId)
                    ->avg('nota') ?? 'Não avaliado';
                    
                if ($satisfacaoMedia !== 'Não avaliado') {
                    $satisfacaoMedia = round($satisfacaoMedia, 1);
                }
            } else {
                $satisfacaoMedia = 'Não avaliado';
            }
        } catch (\Exception $e) {
            $satisfacaoMedia = 'Não avaliado';
        }
        
        return [
            'total_chamados' => $totalChamados,
            'tempo_medio' => $tempoMedio,
            'satisfacao_media' => $satisfacaoMedia
        ];
    }
}
