<?php

namespace App\Services\Relatorios;

use App\Services\RelatorioServicos;
use App\Models\Servico;
use App\Services\ServicoServicos;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use App\Models\Chamado;
use App\Models\HabilitarUnidadeOrganizacional;

class Relatorio_servicos_servicos extends RelatorioServicos
{
    private const MAPEAMENTO_CRITICIDADE = [
        1 => 'Baixa',
        2 => 'Normal',
        3 => 'Importante',
        4 => 'Crítico'
    ];

    public function __construct(
        protected Pdf $pdf, 
        protected ServicoServicos $servicoServicos
    ) {
    }

    public function gerarRelatorioServicos($view, $ordenacao, $filtros)
    {
        // Buscar serviços com base nos filtros
        $query = Servico::query();
        
        // Filtrar por status se especificado
        if (isset($filtros['servico_status']) && $filtros['servico_status'] !== 'todos') {
            $status = ($filtros['servico_status'] === 'habilitados');
            $query->where('status', $status);
        }
        
        // Obter todos os serviços filtrados
        $servicos = $query->get();

        // Verificar se existem serviços
        if ($servicos->isEmpty()) {
            return [
                'status' => 'error',
                'message' => 'Não foram encontrados serviços com os filtros selecionados.'
            ];
        }
      
        // Adicionar estatísticas de chamados
        $servicos = $this->adicionarEstatisticasChamados($servicos);
        
        // Calcular estatísticas gerais
        $totalServicos = Servico::count();
        $totalFiltrados = $servicos->count();
        $porcentagem = ($totalServicos > 0) ? ($totalFiltrados / $totalServicos) * 100 : 0;
        
        // Obter a unidade organizacional para o relatório
        $unidadeOrganizacional = $this->obterUnidadeOrganizacional();
        
        // Obter o setor responsável para o relatório
        $setorResponsavel = $this->obterSetorResponsavel();
        
        // Gerar índices para o relatório
        $indices = $this->gerarIndicesFiltros($filtros);
        $indices['Unidade Organizacional'] = $unidadeOrganizacional;
        $indices['Setor Responsável'] = $setorResponsavel;
        
        $estatisticasGerais = [
            'total_filtrados' => $totalFiltrados,
            'porcentagem' => $porcentagem
        ];
        
        // Preparar dados para o relatório com base na ordenação
        $dadosOrdenados = $this->ordenarDadosRelatorio($servicos, $ordenacao);
        
        // Gerar o relatório com base na view, ordenação e filtros
        return $this->gerarVisualizacaoRelatorio($view, [
            'ordenacao' => $ordenacao,
            'indices' => $indices,
            'estatisticasGerais' => $estatisticasGerais,
            'dadosOrdenados' => $dadosOrdenados,
            'dataGeracao' => Carbon::now()->format('d/m/Y H:i')
        ]);
    }


    private function ordenarDadosRelatorio(Collection $servicos, string $ordenacao): array
    {
        return $ordenacao === 'status' 
            ? $this->agruparPorStatus($servicos)
            : $this->agruparPorCriticidade($servicos);
    }

    private function agruparPorStatus(Collection $servicos): array
    {
        $servicosHabilitados = $servicos->where('status', true);
        $servicosDesabilitados = $servicos->where('status', false);
        
        $servicosHabilitados = $this->servicoServicos->alternarValorNomeCriticidade($servicosHabilitados);
        $servicosDesabilitados = $this->servicoServicos->alternarValorNomeCriticidade($servicosDesabilitados);
        
        $totalFiltrados = $servicos->count();
        
        return [
            'habilitados' => $this->calcularEstatisticasGrupo($servicosHabilitados, $totalFiltrados),
            'desabilitados' => $this->calcularEstatisticasGrupo($servicosDesabilitados, $totalFiltrados)
        ];
    }

    private function agruparPorCriticidade(Collection $servicos): array
    {
        $dadosOrdenados = [];
        $totalFiltrados = $servicos->count();
        
        foreach (self::MAPEAMENTO_CRITICIDADE as $nivel => $nome) {
            $servicosPorCriticidade = $servicos->where('criticidade', $nivel);
            $servicosPorCriticidade = $this->servicoServicos->alternarValorNomeCriticidade($servicosPorCriticidade);
            
            $dadosOrdenados[$nome] = $this->calcularEstatisticasGrupo($servicosPorCriticidade, $totalFiltrados);
        }
        
        return $dadosOrdenados;
    }

    private function calcularEstatisticasGrupo(Collection $grupo, int $total): array
    {
        return [
            'servicos' => $grupo,
            'quantidade' => $grupo->count(),
            'percentual' => ($total > 0) ? ($grupo->count() / $total) * 100 : 0
        ];
    }

    /**
     * Obtém a unidade organizacional atual do usuário ou do sistema
     */
    private function obterUnidadeOrganizacional()
    {
        if (auth()->check() && auth()->user()->setor) {
            $setorId = auth()->user()->setor->id_setor;
            
            $unidade = HabilitarUnidadeOrganizacional::with('setor')
                ->where('habilitado', true)
                ->where('unidade_id', $setorId)
                ->first();
                
            if ($unidade && $unidade->setor) {
                return $unidade->setor->ds_nomesetor ?? 'Não definida';
            }
        }
        
        return 'Não definida';
    }

    /**
     * Obtém o setor responsável atual do usuário
     */
    private function obterSetorResponsavel()
    {
        // Verificar se o usuário está autenticado e tem um setor associado
        if (auth()->check() && auth()->user()->setor) {
            return auth()->user()->setor->ds_nomesetor ?? 'Não definido';
        }
        
        return 'Não definido';
    }

    /**
     * Adiciona estatísticas de chamados aos serviços
     */
    private function adicionarEstatisticasChamados($servicos)
    {
        foreach ($servicos as $servico) {
            $chamados = $servico->chamados;
            
            $servico->total_chamados = $chamados->count();
            
            $chamadosConcluidos = $chamados->filter(function($chamado) {
                return $chamado->concluded_at !== null;
            });
            
            if ($chamadosConcluidos->isNotEmpty()) {
                $tempoTotal = $chamadosConcluidos->sum(function($chamado) {
                    return Carbon::parse($chamado->created_at)
                        ->diffInDays(Carbon::parse($chamado->concluded_at));
                });
            
                $servico->tempo_medio = round($tempoTotal / $chamadosConcluidos->count(), 1);
            } else {
                $servico->tempo_medio = 0;
            }
            
            $servico->satisfacao_media = 'Não avaliado';
        }
        
        return $servicos;
    }

    /**
     * Gera os índices de filtros para o relatório
     */
    private function gerarIndicesFiltros($filtros)
    {
        $indices = [];
        
        // Adicionar filtros aplicados
        foreach ($filtros as $chave => $valor) {
            if (!empty($valor) && isset($this->getDicionarioFiltros()['servicos']['filtro'][$chave])) {
                $nomeIndice = $this->getDicionarioFiltros()['servicos']['filtro'][$chave];
                
                if (isset($this->getDicionarioFiltros()['servicos']['valores'][$valor])) {
                    $indices[$nomeIndice] = $this->getDicionarioFiltros()['servicos']['valores'][$valor];
                } else {
                    $indices[$nomeIndice] = $valor;
                }
            }
        }
        
        // Adicionar data de geração do relatório
        $indices['Data de geração'] = now()->format('d/m/Y H:i');
        
        return $indices;
    }

    /**
     * Retorna o dicionário de filtros para relatórios de serviços
     */
    private function getDicionarioFiltros()
    {
        return [
            "servicos" => [
                "filtro" => [
                    "servico_status" => "Status",
                    "servico_criticidade" => "Criticidade"
                ],
                "valores" => [
                    "todos" => "Todos os serviços",
                    "habilitados" => "Serviços habilitados",
                    "desabilitados" => "Serviços desabilitados"
                ]
            ]
        ];
    }
}
