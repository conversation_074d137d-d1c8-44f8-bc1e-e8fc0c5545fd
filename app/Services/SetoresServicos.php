<?php

namespace App\Services;

use App\Models\SGS\Setor;
use App\Repositories\Contracts\SetoresIRepositorios;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class SetoresServicos
{
    public function __construct(protected SetoresIRepositorios $setoresIRepositorios)
    {
    }

    public function ObterTodos(): ?Collection
    {
        return $this->setoresIRepositorios->ObterTodos();
    }

    public function getSetores(string $setorId): Builder
    {
        return $this->setoresIRepositorios->getSetores($setorId);
    }

    public function getUnidadeOrganizacional(array $unidadesOrganizacional = [605, 40]): Builder
    {
        return $this->setoresIRepositorios->getUnidadeOrganizacional($unidadesOrganizacional);
    }

    public function verificarUnidadeOrganizacionalHabilitada(string $setorId): int|null
    {
        return $this->setoresIRepositorios->verificarUnidadeOrganizacionalHabilitada($setorId);
    }

    public function getSetoresFilhosIds(string $setorId): array
    {
        return $this->setoresIRepositorios->getSetoresFilhosIds($setorId);
    }
}