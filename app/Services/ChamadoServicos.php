<?php 

namespace App\Services;

use App\Models\Chamado;
use App\Models\SGS\{Pessoa,Usuario};
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Services\SGS\SetoresServicos;

class ChamadoServicos 
{

    public function __construct(private Chamado $chamadoModel, private SetoresServicos $setoresServicos) {

    }


    public function validaValoresPadrao($request)
    {
        if(is_array($request)){
            $filtro = $request;
        }else{
            $filtro = $request->all();
        }
        

        if(empty($filtro))
        {
            $filtro = [
                 
                'chamado_titulo' => null,
                'chamado_abertura' => null,
                'chamado_responsavel' => null,
                'chamado_requisitante' => null,
                'chamado_servico' => 'todos',
                'chamado_status' => 'Aberto',
                'chamado_direcionamento'=>"direcionado_usuario",
                'ordenacao' => 'data',
                "filtro_conclusao" => "ultimos_10",
                'data_conclusao_inicio' => null,
                'data_conclusao_fim' => null,
            ];
        }
      
        return $filtro;
    }
   
   

    public function contaChamadosRegistrados(){
        return $this->chamadoModel->count();
    }
    public function localizarChamadosPeloFiltro(array $filtro) 
    {
     
        //tratamento para alteração de direcionamento
        if($filtro['chamado_direcionamento'] === 'setor')
        {
            $filtro['chamado_status'] = 'Aberto';

        }else if ($filtro['chamado_direcionamento'] !== 'setor' && $filtro['chamado_direcionamento'] !== 'criado_usuario'  && $filtro['chamado_status'] === 'Aberto' && !in_array('chamados-listar-todos', array_column(auth()->user()->PermissoesUsuarioLogado('ocultas'), 'descricao'))                              )
        {
            $filtro['chamado_status'] = 'Em andamento';
        }
        
        
        // Tratamento da data de abertura
        if (isset($filtro['chamado_abertura'])) {
            $filtro['chamado_abertura'] = Carbon::parse($filtro['chamado_abertura'])->format('Y-m-d');
        }

        // Validação de datas de conclusão
        if (isset($filtro['data_conclusao_inicio'])) {
            $dataInicio = Carbon::parse($filtro['data_conclusao_inicio']);
            if ($dataInicio->isFuture()) {
                $filtro['data_conclusao_inicio'] = Carbon::now()->format('Y-m-d');
            }
        }
        if (isset($filtro['data_conclusao_fim'])) {
            $dataFim = Carbon::parse($filtro['data_conclusao_fim']);
            if ($dataFim->isFuture()) {
                $filtro['data_conclusao_fim'] = Carbon::now()->format('Y-m-d');
            }

            // Verifica se a data final é anterior à data inicial (verificação de segurança)
            if (isset($filtro['data_conclusao_inicio']) && $dataFim->lt(Carbon::parse($filtro['data_conclusao_inicio']))) {
                
                $filtro['data_conclusao_fim'] = $filtro['data_conclusao_inicio'];
            }
        }

        $permissoes = auth()->user()->PermissoesUsuarioLogado('ocultas');
        // Busca de setores e usuários
        $setoresAtendente = $this->buscarSetoresPorNome($filtro['chamado_atendente'] ?? null);
        $setoresSolicitante = $this->buscarSetoresPorNome($filtro['chamado_solicitante'] ?? null);
        $usuarios = $this->buscarUsuariosPorNome($filtro['chamado_usuario_solicitante'] ?? null);

 
        $query = Chamado::query()
            ->when(!in_array('chamados-listar-todos', array_column($permissoes, 'descricao')), 
                fn($query) => $this->aplicarFiltroPermissao($query, $permissoes, $filtro))
            ->when(isset($filtro['chamado_status']) && $filtro['chamado_status'] !== "Todos", 
                function($query) use ($filtro) {
                    if ($filtro['chamado_status'] === 'Concluido') {
                        $query->where('chamados.status', 'Concluido');
                        
                        // Verifica qual opção de filtro foi selecionada
                        if (isset($filtro['filtro_conclusao'])) {
                            if ($filtro['filtro_conclusao'] === 'ultimos_10') {
                                // Busca os últimos 10 chamados concluídos
                                $query->orderBy('concluded_at', 'desc')
                                      ->limit(10);
                            } else if ($filtro['filtro_conclusao'] === 'periodo') {
                                // Busca por período de conclusão
                                if (isset($filtro['data_conclusao_inicio'])) {
                                    $query->whereDate('concluded_at', '>=', Carbon::parse($filtro['data_conclusao_inicio']));
                                }
                                if (isset($filtro['data_conclusao_fim'])) {
                                    $query->whereDate('concluded_at', '<=', Carbon::parse($filtro['data_conclusao_fim']));
                                }
                            }
                        } else {
                            // Se não houver opção selecionada, busca os últimos 10 por padrão
                            $query->orderBy('concluded_at', 'desc')
                                  ->limit(10);
                        }
                    } else {
                        $query->where('chamados.status', $filtro['chamado_status']);
                    }
                })
                
            ->when(isset($filtro['chamado_direcionamento']), 
                fn($query) => $this->aplicarFiltroDirecionamento($query, $filtro['chamado_direcionamento']))
            ->when(isset($filtro['chamado_titulo']), 
                fn($query) => $query->where('chamados.titulo', 'ilike', '%' . $filtro['chamado_titulo'] . '%'))
            ->when(isset($filtro['chamado_abertura']), 
                fn($query) => $query->whereDate('chamados.created_at', $filtro['chamado_abertura']))
            ->when(isset($filtro['chamado_atendente']) && in_array('chamados-listar-setor', array_column($permissoes, 'descricao')), 
                fn($query) => $query->whereIn('chamados.setor_atendente_id', $setoresAtendente))
            ->when(isset($filtro['chamado_solicitante']), 
                fn($query) => $query->whereIn('chamados.setor_solicitante_id', $setoresSolicitante))
            ->when(isset($filtro['chamado_usuario_solicitante']), 
                fn($query) => $this->aplicarFiltroUsuarioSolicitante($query, $usuarios))
            ->when(isset($filtro['chamado_servico']) && $filtro['chamado_servico'] !== 'todos', 
                fn($query) => $query->where('chamados.servico_id', $filtro['chamado_servico']));

        // Aplicar ordenação
        if (isset($filtro['ordenacao'])) {
            if ($filtro['ordenacao'] === 'data') {
                if ($filtro['chamado_status'] === 'Concluido') {
                    $query->orderBy('concluded_at', 'desc');
                } else {
                    $query->orderBy('chamados.created_at', 'asc');
                }
            } else if ($filtro['ordenacao'] === 'criticidade') {
                $query->join('servicos', 'chamados.servico_id', '=', 'servicos.id')
                      ->orderBy('servicos.criticidade', 'desc')
                      ->select('chamados.*');
            }
        } else {
            // Ordenação padrão por data
            if ($filtro['chamado_status'] === 'Concluido') {
                $query->orderBy('concluded_at', 'desc');
            } else {
                $query->orderBy('chamados.created_at', 'desc');
            }
        }

        // Debug da query final
        // dump('SQL Query:', $query->toSql());
        // dump('Bindings:', $query->getBindings());

        //  dd($query->toSql(), $query->getBindings());
        return $query->paginate(10);
    }

    private function buscarSetoresPorNome(?string $nome): ?array
    {
        if (!$nome) {
            return null;
        }

        return DB::connection('pgsql_sgs')
            ->table('setores')
            ->where('ds_nomesetor', 'ILIKE', '%' . $nome . '%')
            ->pluck('id_setor')
            ->toArray();
    }

    private function buscarUsuariosPorNome(?string $nome): ?array
    {
        if (!$nome) {
            return null;
        }

        return Usuario::whereHas('pessoa', function ($query) use ($nome) {
            $query->where('ds_nomepessoa', 'ILIKE', '%' . $nome . '%');
        })->pluck('id_usuario')->toArray();
    }

    private function aplicarFiltroPermissao($query, array $permissoes, array $filtro)
    {
        if (in_array('chamados-listar-setor', array_column($permissoes, 'descricao')) && $filtro['chamado_direcionamento'] === "setor") {
            return $query->where('setor_atendente_id', auth()->user()->unidade_exercicio?->id_setor);
        }
        
        // Se for direcionado ao usuário, não aplica o filtro de solicitante
        if ($filtro['chamado_direcionamento'] === 'direcionado_usuario') {
            return $query;
        }
        
        return $query->where('usuario_solicitante_id', auth()->user()->id_usuario);
    }

    private function aplicarFiltroDirecionamento($query, string $direcionamento)
    {

        $query->whereNotNull('chamados.servico_id');
        
        return match($direcionamento) {
            'direcionado_usuario' => $query->where('chamados.usuario_atendente_id', auth()->user()->id_usuario),
            'criado_usuario' => $query->where('chamados.usuario_solicitante_id', auth()->user()->id_usuario),
            'setor' => $query->whereNull('chamados.usuario_atendente_id')->whereIn('chamados.status', ['Aberto']),
            default => $query->whereNull('chamados.usuario_atendente_id')
        };

       
    }

    private function aplicarFiltroUsuarioSolicitante($query, ?array $usuarios)
    {
        return is_array($usuarios) 
            ? $query->whereIn('chamados.usuario_solicitante_id', $usuarios) 
            : $query->where('chamados.usuario_solicitante_id', (int) $usuarios);
    }

    public function selecionaChamadoPeloId($id) {
        $chamado = $this->chamadoModel->find($id);
    
        return $chamado;
    }

    public function selecionarChamadosPorPeriodo($dataInicio, $dataFim) {
        $chamados = Chamado::query()
            ->whereBetween('concludet_at', [$dataInicio, $dataFim])
            ->get();
        return $chamados;
    }


    // /**
    //  * Realiza o upload de imagem para um chamado
    //  * 
    //  * @param \Illuminate\Http\UploadedFile $imagem Arquivo de imagem
    //  * @param string|null $servicoId ID do serviço associado ao chamado
    //  * @param string $siglaSetor Sigla do setor para o diretório de armazenamento
    //  * @param array|null $tombos Array de tombos associados ao chamado
    //  * @return string|null Caminho do arquivo salvo ou null se falhar
    //  */
    public function uploadImagemChamado($imagem, $servicoId, $siglaSetor, $tombos = [])
    {
        if (!$imagem || !$imagem->isValid()) {
            return null;
        }
        $extensao = $imagem->extension();
        // Formata os tombos para o nome do arquivo
        $tombosStr = empty($tombos) ? '' : implode('-', $tombos);
        // Cria o nome do arquivo com o padrão: data_hora_S{servicoId}_T_{tombos}.extensao
        $nomeArquivo = date("Y_m_d_H_i_s_") . "S" . ($servicoId ?? '') . "_T_" . $tombosStr . "." . $extensao;
        // Salva o arquivo no diretório específico do setor
        return $imagem->storeAs("images/chamados/{$siglaSetor}", $nomeArquivo, 'public');
    }
}
