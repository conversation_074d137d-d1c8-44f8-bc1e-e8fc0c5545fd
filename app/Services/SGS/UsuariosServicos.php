<?php

namespace App\Services\SGS;

use App\Repositories\Contracts\UsuariosIRepositorios;
use Exception;

class UsuariosServicos
{
    public function __construct(protected UsuariosIRepositorios $usuarioRepositorio)
    {
    }

    public function Login(array $credenciais): void
    {
        $usuario = $this->usuarioRepositorio->ObterUsuario($credenciais['ds_login']);

        $senha = $usuario->ds_login.$credenciais['ds_senha'];
        $senhaHash = sha1($senha);

        if (! $usuario || $usuario->ds_senha !== $senhaHash) {
            throw new Exception('Login ou senha incorretos.');
        }

        auth()->login($usuario);
    }

    public function Logout(): void
    {
        if (auth()->check()) {
            auth()->logout();
            request()->session()->invalidate();
            request()->session()->regenerateToken();
        }
    }
}
