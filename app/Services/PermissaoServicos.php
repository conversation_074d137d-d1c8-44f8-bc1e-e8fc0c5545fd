<?php

namespace App\Services;

use App\Models\Permissao;
use App\Repositories\Contracts\PermissaoIRepositorios;
use Illuminate\Database\Eloquent\Collection;

class PermissaoServicos
{   
    public function __construct(protected PermissaoIRepositorios $permissaoRepositorios)
    { 
    }

    public function obterTodos(): ?Collection
    {
        return $this->permissaoRepositorios->obterTodos();
    }

    public function obterPorId(int $id): ?Permissao
    {
        return $this->permissaoRepositorios->obterPorId($id);
    }
}
