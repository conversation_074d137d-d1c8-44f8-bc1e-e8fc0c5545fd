<?php

namespace App\Services;

use App\Models\Perfil;
use App\Repositories\Contracts\{PerfilIRepositorios};
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;

class PerfilServicos
{  
    public function __construct(protected PerfilIRepositorios $perfilRepositorio)
    {        
    }

    public function obterTodos(): ?Collection
    {
        return $this->perfilRepositorio->obterTodos();
    }

    public function obterPorId(int $id): ?Perfil
    {
        return $this->perfilRepositorio->obterPorId($id);
    }

    public function excluir(int $id): void
    {
        $this->perfilRepositorio->excluir($id);
    }

    public function salvar(array $perfil, ?array $permissoes): ?Perfil
    {
        $perfilNovo = $this->perfilRepositorio->salvar($perfil);       

        $this->sincronizarPermissoes($perfilNovo->id, $permissoes);

        return $perfilNovo;
    }

    public function alterar(int $id, array $perfilAtualizado, ?array $permissoes): ?int
    {
        $quantidadeRegistro = $this->perfilRepositorio->alterar($id, $perfilAtualizado);

        $this->sincronizarPermissoes($id, $permissoes);   
        
        return $quantidadeRegistro;
    }

    public function obterTodosPaginado(int $quantidadeRegistro): ?LengthAwarePaginator
    {
        return $this->perfilRepositorio->obterTodosPaginado($quantidadeRegistro);
    }  

    public function sincronizarPermissoes(int $id, ?array $permissoes) : void
    { 
        $this->perfilRepositorio->sincronizarPermissoes($id, $permissoes);             
    }
}
