<?php

namespace App\Services;

use App\Models\HabilitarUnidadeOrganizacional ;
use Illuminate\Database\Eloquent\Collection;

class HabilitarUnidadesOrganizacionaisServicos
{
    public function __construct(protected HabilitarUnidadeOrganizacional $model)
    {}

    public function getSetoresHabilitados(): ?Collection
    {
        return $this->model::with('setor')
            ->where('habilitado', true)->get()
            ->sortBy(fn ($setor) => $setor->setor->ds_nomesetor);
    }
}