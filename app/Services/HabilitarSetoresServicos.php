<?php

namespace App\Services;

use App\Models\HabilitarSetor;
use App\Repositories\Contracts\HabilitarSetoresIRepositorios;
use Illuminate\Database\Eloquent\Collection;

class HabilitarSetoresServicos
{
    public function __construct(protected HabilitarSetoresIRepositorios $habilitarSetoresIRepositorio)
    {
    }

    public function ObterTodosPorHabilitacao(bool $habilitado): ?Collection
    {
        return $this->habilitarSetoresIRepositorio->ObterTodosPorHabilitacao($habilitado);
    }

    public function ObterPorIdSetor(int $idSetor): ?HabilitarSetor
    {
        return $this->habilitarSetoresIRepositorio->ObterPorIdSetor($idSetor);
    }
}
