<?php

namespace App\Providers;

use App\Models\Permissao;
use App\Models\SGS\Usuario;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();
        Permissao::get()->map(function ($permissao) {
            Gate::define($permissao->descricao, function (Usuario $usuario) use ($permissao) {
                $permissoes = DB::select('select count(*) as qtd
                                     from perfil_usuario
                                     join perfil_permissao on perfil_permissao.perfil_id = perfil_usuario.perfil_id
                                     where perfil_usuario.id_usuario = '.$usuario->id_usuario.'
                                     and perfil_permissao.permissao_id = '.$permissao->id);

                if ($permissoes[0]->qtd > 0) {
                    return true;
                } else {
                    return false;
                }

            });
        });

        //
    }
}
