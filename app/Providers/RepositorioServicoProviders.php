<?php

namespace App\Providers;

use App\Repositories\Contracts\{HabilitarSetoresIRepositorios, PerfilIRepositorios, PermissaoIRepositorios, SetoresIRepositorios, UsuariosIRepositorios};
use App\Repositories\{HabilitarSetoresRepositorios, PerfilRepositorios, PermissaoRepositorios, SetoresRepositorios, UsuariosRepositorios};
use Illuminate\Support\ServiceProvider;

class RepositorioServicoProviders extends ServiceProvider
{
    public $bindings = [
        UsuariosIRepositorios::class            =>      UsuariosRepositorios::class,
        PerfilIRepositorios::class              =>      PerfilRepositorios::class,
        HabilitarSetoresIRepositorios::class    =>      HabilitarSetoresRepositorios::class,
        PermissaoIRepositorios::class           =>      PermissaoRepositorios::class,
        SetoresIRepositorios::class             =>      SetoresRepositorios::class
    ];

    public function boot()
    {
        //Função de boot (atualmente sem uso)
    }
}
