<?php

namespace App\Http\Controllers;

use App\Models\{Chamado, HabilitarSetor, HabilitarUnidadeOrganizacional, ChamadoMensagem};
use App\Services\{ChamadoHistoricoServicos,ChamadoTomboServicos, SGS\SetoresServicos, ServicoServicos, ChamadoServicos};
use App\Util\{Notificacao, TratarExcecao};
use Exception;
use Illuminate\Http\Request;

class RegulacaoController extends Controller
{
    public function __construct(
        protected SetoresServicos $setoresServicos,
        protected ChamadoHistoricoServicos $chamadoHistoricoServicos,
        protected ChamadoTomboServicos $chamadoTomboServicos,
        protected ServicoServicos $servicoServicos,
        protected ChamadoServicos $chamadoServicos
    )
    {}

    public function index()
    {
        $id = $this->setoresServicos->verificarUnidadeOrganizacionalHabilitada(auth()->user()->setor?->id_setor);
        $chamados = Chamado::with('usuarioSolicitante','usuarioSolicitante.pessoa','setorSolicitante')->where('setor_atendente_id', $id)->where('servico_id', NULL)->orderBy('created_at', 'desc')->paginate();

        $unidade = HabilitarUnidadeOrganizacional::where('unidade_id', $id)->first();
        if (!$unidade) {
            Notificacao::mensagem('error', 'Unidade Organizacional não encontrada');
            return redirect()->back();
        }
        return view('regulacao.index', compact('chamados', 'unidade'));
    }

    public function edit($id)
    {
        $chamado = Chamado::findOrFail($id);
        $unidade = HabilitarUnidadeOrganizacional::where('unidade_id', $chamado->setor_atendente_id)->first();
        $setoresFilhosIds = $this->setoresServicos->getSetoresFilhosIds($chamado->setor_atendente_id);
        $setores = HabilitarSetor::with('setor')->whereIn('setor_id', $setoresFilhosIds)->where('habilitado', true)->get()->sortBy(fn ($setor) => $setor->setor->ds_nomesetor); 
        
        return view('regulacao.edit', compact('chamado', 'unidade', 'setores'));
    }

    public function update(Request $request, string $id)
    {
        $chamado = Chamado::with('setorAtendente')->findOrFail($id);
        $request->validate([
            'setorId' => 'required|exists:pgsql_sgs.setores,id_setor',
            'servico_id' => 'required|exists:servicos,id',
            'observacao' => 'nullable|string',
            'imagem' => 'nullable|image|mimes:jpeg,png,jpg|max:1024',
        ],[],[
            'servico_id' => 'serviço',
            'setorId' => 'setor',
            'observacao' => 'informação adicional',
        ]);
        
        try {
            // Upload da imagem usando o serviço
            $caminhoArquivo = null;
            if ($request->hasFile('imagem')) {
                // Obtém os tombos associados ao chamado
                $tombos = $this->chamadoTomboServicos->getTombosByChamadoId($chamado->id_chamado);
                
                $caminhoArquivo = $this->chamadoServicos->uploadImagemChamado($request->imagem, $request->servico_id, $chamado->setorAtendente->sg_setor, $tombos);
            }
            $infoAdicional = [
                'observacao' => $request->observacao,
                'caminho_arquivo' => $caminhoArquivo ?? null,
                'usuario_atendente_id' => auth()->id(),
                'atividade' => 'Encaminhado para o setor',
            ];
    
            $chamado->update(['servico_id' => $request->servico_id, 'setor_atendente_id' => $this->servicoServicos->getSetorResponsavelId($request->servico_id)]);
            $this->chamadoHistoricoServicos->create($chamado->toArray(), $infoAdicional);
            
            if(isset($infoAdicional['caminho_arquivo']) || isset($infoAdicional['observacao'])){
                ChamadoMensagem::create([
                    'chamado_id' => $chamado->id_chamado,
                    'mensagem' => $infoAdicional['observacao'],
                    'usuario_id' => $infoAdicional['usuario_atendente_id'],
                    'tipo_usuario' => 'Atendente',
                    'tipo_mensagem' => 'Informacao adicional',
                    'caminho_arquivo' => $infoAdicional['caminho_arquivo'],
                ]);
            }

            Notificacao::mensagem('success', 'Chamado encaminhado com sucesso.');
            
            return redirect()->route('regulacao.index');
            
        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
    }
}
