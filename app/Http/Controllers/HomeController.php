<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Symfony\Component\CssSelector\Parser\Shortcut\ElementParser;
use App\Services\{ChamadoServicos, SGS\SetoresServicos};
use App\Models\Servico;
use Hamcrest\Core\Set;

class HomeController extends Controller
{

    protected $chamadoServicos ;
    public function __construct(ChamadoServicos $chamadoServicos, protected SetoresServicos $setoresServicos)
    {
        $this->chamadoServicos =$chamadoServicos;
    }
    public function index(Request $request)
    {

        
        $filtro = FormSaveInSession($request->all(), 'chamados',$this->chamadoServicos->validaValoresPadrao([]));
        
        $chamados = $this->chamadoServicos->localizarChamadosPeloFiltro($filtro);
        $servicos = Servico::query()->servicoSetores([auth()->user()->lotacao?->id_setor_exercicio])->get();
        
        return view('home.index', compact('filtro','chamados','servicos'));
    }


    public function show($id){

        $statusConfig = [
            'Aberto' => ['class' => 'alert-secondary', 'icon' => 'fa-info-circle'],
            'Em andamento' => ['class' => 'alert-info', 'icon' => 'fa-spinner fa-spin'],
            'Parado' => ['class' => 'alert-warning', 'icon' => 'fa-pause-circle'],
            'Concluido' => ['class' => 'alert-success', 'icon' => 'fa-check-circle'],
            'Solicitado fechamento' => ['class' => 'alert-danger', 'icon' => 'fa-pause-circle'],
        ];
        $servicoConfig = [
            0 => ['class' => '', 'status' => ''],
            1 => ['class' => 'bg-success', 'status' => 'Baixa'],
            2 => ['class' => 'bg-info', 'status' => 'Normal'],
            3 => ['class' => 'bg-warning', 'status' => 'Importante'],
            4 => ['class' => 'bg-danger', 'status' => 'Crítica'],
        ];
        
        $chamado = $this->chamadoServicos->selecionaChamadoPeloId($id);
        $chamado->infoAdicional = $chamado->mensagens()->where('tipo_mensagem', 'Informacao adicional')->value('mensagem');

        $chamado->statusClass = $statusConfig[$chamado->status]['class'];
        $chamado->statusIcon = $statusConfig[$chamado->status]['icon'];
        $chamado->servicoClass = $servicoConfig[$chamado->servico->criticidade ?? 0]['class'];
        $chamado->servicoStatus = $servicoConfig[$chamado->servico->criticidade ?? 0]['status'];


        return view('home.requestsInfo', compact('chamado'));   
    }


}
