<?php

namespace App\Http\Controllers;

use App\Util\TratarExcecao;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Contracts\View\View;
use App\Util\Notificacao;
use App\Models\Servico;
use Illuminate\Support\Facades\DB;
use App\Models\SGS\Setor;
use App\Services\ServicoServicos;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\ValidationException;




define('SERVICO_APRESENTADO_PADRAO', new Request(['apresentar' => 'habilitados']));

define('ROTAS', [
    "manutencao" => 'administrador.servicos.manutencao'
]);
class ServicoController extends Controller
{
    protected $servicoServicos ;
    public function __construct(ServicoServicos $servicoServicos)
    {
        $this->servicoServicos = $servicoServicos;
    }

   
    public function index(Request $request ): View
    {

        if(Gate::denies('servicos-listar')){
            abort(403);
        }

        FormSaveInSession($request, 'servicos');

        if(auth()->user()->perfis()->where('nome', 'Chefe de Setor')->exists() || auth()->user()->perfis()->where('nome', 'Administrador')->exists()){
            $setor_do_servico = $request ['servico_setor'] ?? 'todos';
        }else{
            $setor_do_servico = auth()->user()->lotacao?->id_setor_exercicio;
        }
      
        $servicos = $this->servicoServicos->buscarServicosStatus($request['servico_status'] ?? 'habilitados', $setor_do_servico); 
        $servicos = $this->servicoServicos->alternarIdNomeSetor($servicos);
        $servicos = $this->servicoServicos->alternarValorNomeCriticidade($servicos);
        
        $setores = [];
        $setores = $this->servicoServicos->ListaSetoresDosServicosCriados();

        $exibirListagemSetores = auth()->user()->perfis()->where('nome', 'Administrador')->exists() || auth()->user()->perfis()->where('nome', 'Chefe de Setor')->exists();

        return view('administrador.servicos.index',compact('servicos','setores','exibirListagemSetores'));
    }

    public function create()
    {
        if(Gate::denies('servicos-criar')){
            abort(403);
        }
    
        $atualizacao = false;
        $visualizacao = false;
        $servico = null;
        $rota = route('servicos.store');
        $atividade = 'Criar';
        
        $setores = $this->servicoServicos->selecionaSetoresHabilitados();
        $campusSetoresHabilitados =$this->servicoServicos->listaCampusDisponiveis($setores);
        $setores = $this->servicoServicos->alternaIdNomeCampus($setores);
        
        return view(ROTAS['manutencao'],[
            
            'setores'=>$setores,
            'rota'=>$rota,
            'atualizacao'=>$atualizacao,
            'atividade'=>$atividade,
            'visualizacao'=>$visualizacao,
            'servico'=>$servico,
            'campusSetoresHabilitados'=>$campusSetoresHabilitados
        
        ]);


    }

    public function store(Request $request)
    {
        if(Gate::denies('servicos-criar')){
            abort(403);
        }
        $request->validate([
            'nome' => ['required', 'max:100'],
            'descricao' => ['required','max:255'],
            'finalInputField' => ['required'],
            'criticidade' => ['required'],
            'status' => ['required'],
        ], [], [
            'descricao' => 'atividade realizada',
            'finalInputField' => 'setor responsável',
        ]);
        
        $dadosPerfil = $request->only('nome', 'descricao','finalInputField','criticidade','status');
        
        //pegando o ID so setor responsavel pelo nome e verificando se o setor existe
        $dadosPerfil['finalInputField'] = DB::connection('pgsql_sgs')
        ->table('setores')
        ->where('ds_nomesetor', $dadosPerfil['finalInputField'])
        ->pluck('id_setor')
        ->first();
        
        if(!$dadosPerfil['finalInputField']){
            Notificacao::mensagem('error', 'Setor informado inexistente.');
            return redirect()->back()->withInput();
        }
        
        try {
            Servico::create([
                'nome' => $dadosPerfil['nome'],
                'descricao' => $dadosPerfil['descricao'],
                'setor_responsavel' => $dadosPerfil['finalInputField'],
                'criticidade' => $dadosPerfil['criticidade'],
                'status' => $dadosPerfil['status'],
            ]);
            Notificacao::mensagem('success', 'Serviço cadastrado com sucesso.');
            return redirect()->route('servicos.index');
        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
      
        

    }

    public function show(string $id)
    {

        $servico = Servico::find($id);
        $atualizacao = false;
        $visualizacao = true;
        $atividade = 'Visualizar';
        $rota = '#';

        if(Gate::denies('servicos-visualizar')){
            abort(403);
        }

        if(!$servico){
            return $this->index(SERVICO_APRESENTADO_PADRAO);
        }

        //pegando nome do setor pelo ID
         $servico = $this->servicoServicos->alternarIdNomeSetor($servico);

         
        return view(ROTAS['manutencao'],compact('atualizacao','visualizacao','rota','servico','atividade'));
    }

    public function edit(string $id)
    {
      if(Gate::denies('servicos-editar')){
            abort(403);
        }
        //verrificação da existência do serviço e chamando a view
        if(!$servico = Servico::all()->find($id)){
            return $this->index(SERVICO_APRESENTADO_PADRAO);
        }

        //pegando nome do setor pelo ID
        $servico->setor_responsavel = DB::connection('pgsql_sgs')
            ->table('setores')
            ->where('id_setor', $servico->setor_responsavel)
            ->pluck('ds_nomesetor')
            ->first();
            
        $atualizacao = true;
        $visualizacao = false;
        $rota = route('servicos.update', $id);
        $atividade = 'Editar';

       //Selecionando apenas os setores habilitados com o query builder
       $setoresHabilitados = DB::connection('pgsql')
            ->table('habilitar_setores')
            ->where('habilitado', true)
            ->pluck('setor_id');
   
        $setores = DB::connection('pgsql_sgs')
            ->table('setores')
            ->whereIn('id_setor', $setoresHabilitados)
            ->get();

      

       //troca o ID do campus pelo nome
       foreach($setores as $setor){
            $setor->id_campus = DB::connection('pgsql_sgs')
            ->table('campi')
            ->where('id_campus', $setor->id_campus)
            ->pluck('ds_nomecampus')
            ->first();

            $campusSetoresHabilitados[] = $setor->id_campus;
        }
        
        $campusSetoresHabilitados = array_unique($campusSetoresHabilitados);
        

            return view(ROTAS['manutencao'],compact('atualizacao','visualizacao','rota','servico','atividade','setores','campusSetoresHabilitados'));
        }

    public function update(Request $request,$id)
    {
        if(Gate::denies('servicos-editar')){
            abort(403);
        }

        $request->validate([
            'nome' => ['required', 'max:100'],
            'descricao' => ['required','max:255'],
            'finalInputField' => ['required'],
            'criticidade' => ['required'],
            'status' => ['required'],
        ], [], [
            'descricao' => 'atividade realizada',
            'finalInputField' => 'setor responsável',
        ]);
        
        $dadosPerfil = $request->only('nome', 'descricao','finalInputField','criticidade','status');
        
        
        try {
            //pegando o ID so setor responsavel pelo nome
            $dadosPerfil['finalInputField'] = DB::connection('pgsql_sgs')
            ->table('setores')
            ->where('ds_nomesetor', $dadosPerfil['finalInputField'])
            ->pluck('id_setor')
            ->first();
        
            $servico = Servico::all()->find($id);
            $servico->update([
                'nome' => $dadosPerfil['nome'],
                'descricao' => $dadosPerfil['descricao'],
                'setor_responsavel' => $dadosPerfil['finalInputField'],
                'criticidade' => $dadosPerfil['criticidade'],
                'status' => $dadosPerfil['status'],
            ]);
            Notificacao::mensagem('success', 'Serviço atualizado com sucesso.');
            return redirect()->route('servicos.index');

        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
    }

    public function destroy($id)
    {
       try {
        $servico = Servico::all()->find($id);
        $servico->delete();
        Notificacao::mensagem('success', 'Serviço apagado com sucesso.');
       } catch (\Throwable $th) {
        Notificacao::mensagem('error', 'Erro ao excluir servico.');
       }
        
       return redirect()->back()->withInput();
    }

    public function getServicos($setorId){
        if(!request()->ajax()){
            abort(403, 'Acesso não autorizado.');
        }

        $servicos = Servico::servicoSetores([$setorId])->orderBy('nome')->get();
        return response()->json($servicos);
    }


}
