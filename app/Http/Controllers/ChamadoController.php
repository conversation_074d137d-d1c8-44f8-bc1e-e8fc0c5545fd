<?php

namespace App\Http\Controllers;

use App\Models\{Chamado, ChamadoTombo, HabilitarUnidadeOrganizacional, Servico, SGS\Campus, ChamadoMensagem};
use App\Services\{ChamadoHistoricoServicos, HabilitarUnidadesOrganizacionaisServicos, SGS\SetoresServicos, ServicoServicos, ChamadoServicos, ChamadoTomboServicos};
use App\Util\{Notificacao, TratarExcecao};
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ChamadoController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    public function __construct(
        protected SetoresServicos $setoresServicos,
        protected HabilitarUnidadesOrganizacionaisServicos $habilitarUnidadesOrganizacionaisServicos,
        protected ChamadoHistoricoServicos $chamadoHistoricoServicos,
        protected ServicoServicos $servicoServicos,
        protected ChamadoServicos $chamadoServicos,
        protected ChamadoTomboServicos $chamadoTomboServicos,
        )
    {}
    public function index()
    {
        $setores = $this->habilitarUnidadesOrganizacionaisServicos->getSetoresHabilitados();
        return view('chamado.index', compact('setores'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create($setorAtendenteId)
    {
        $setorAtendente = HabilitarUnidadeOrganizacional::where('unidade_id', $setorAtendenteId)->firstOrFail();
        $setoresServico = $this->setoresServicos->getSetoresFilhosIds($setorAtendente->unidade_id);
        $servicos = Servico::servicoSetores($setoresServico)->get();
        $campi = Campus::all();
        $usuario = auth()->user();
        
        return view('chamado.create', compact('setorAtendente', 'campi', 'usuario', 'servicos'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        
        $request->validate([
            'servico' => 'nullable|exists:servicos,id',
            'titulo' => 'required|string|max:255',
            'descricao' => 'required|string',
            'setorAtendenteId' => 'required|exists:habilitar_unidades_organizacionais,unidade_id',
            'setorSolicitanteId' => 'required|exists:pgsql_sgs.setores,id_setor',
            'telefone' => 'required|string|max:20',
            'tombos' => 'nullable|array',
            'tombos.*' => ['nullable', 'regex:/^\d{6}$/'],
            'imagem' => 'nullable|image|mimes:jpeg,png,jpg|max:1024',
        ]);
        
        try {
            // Upload da imagem usando o serviço
            $caminhoArquivo = null;
            if ($request->hasFile('imagem')) {
                $caminhoArquivo = $this->chamadoServicos->uploadImagemChamado($request->imagem, $request->servico, $request->siglaSetor, $request->tombos);
            }
    
            $chamado =Chamado::create([
                'servico_id' => $request->servico ?? null,
                'titulo' => $request->titulo,
                'descricao' => $request->descricao,
                'usuario_solicitante_id' => $request->usuarioId,
                'setor_atendente_id' => $request->setorAtendenteId,
                'setor_solicitante_id' => $request->setorSolicitanteId,
                'telefone_contato' => $request->telefone,
                'caminho_arquivo' => $caminhoArquivo ?? null, 
            ]);

            $this->chamadoHistoricoServicos->create($chamado->toArray(), ['atividade' => 'Criado']);

            ChamadoMensagem::create([
                'chamado_id' => $chamado->id_chamado,
                'mensagem' => $chamado->descricao,
                'usuario_id' => $chamado->usuario_solicitante_id,
                'tipo_usuario' => 'Solicitante',
                'tipo_mensagem' => 'Descricao',
                'caminho_arquivo' => $chamado->caminho_arquivo,
            ]);
    
            if(!empty($request->tombos) && !in_array(null, $request->tombos)){
                $tombosData = array_map(fn($tombo) => [
                    'chamado_id' => $chamado->id_chamado,
                    'nm_tombo' => $tombo,
                ], $request->tombos);
    
                ChamadoTombo::insert($tombosData);
            }
    
    
            Notificacao::mensagem('success', 'Chamado criado com sucesso.');
            return redirect()->route('chamado.index');
            
        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $statusConfig = [
            'Aberto' => 'bg-secondary',
            'Em andamento' => 'bg-info',
            'Parado' => 'bg-warning',
            'Concluido' => 'bg-success',
            'Solicitado fechamento' => 'bg-danger',
        ];

        $chamado = $this->chamadoServicos->selecionaChamadoPeloId($id);
        $chamado->statusClass = $statusConfig[$chamado->status];
        $chamado->usuarioAtual = $chamado->usuario_solicitante_id == auth()->user()->id_usuario ? 'Solicitante' : 'Atendente';
        
        $mensagens = $chamado->mensagens()->with('usuario', 'usuario.pessoa')->get();//rever
        $ultimaSolicitacaoFechamento = $mensagens->where('tipo_mensagem', 'Fechamento solicitado')->sortByDesc('created_at')->first()->id ?? null;
        return view('chamado.show', compact('chamado', 'mensagens', 'ultimaSolicitacaoFechamento'));
    }

    public function storeMessage(Request $request, string $id)
    {
        $request->validate([
            'mensagem' => 'required|string',
            'imagem' => 'nullable|image|mimes:jpeg,png,jpg|max:1024',
        ]);

        try {
            $chamado = $this->chamadoServicos->selecionaChamadoPeloId($id);
            
            // Upload da imagem usando o serviço
            $caminhoArquivo = null;
            if ($request->hasFile('imagem')) {
                // Obtém os tombos associados ao chamado
                $tombos = $this->chamadoTomboServicos->getTombosByChamadoId($id);
                
                $caminhoArquivo = $this->chamadoServicos->uploadImagemChamado($request->imagem, $chamado->servico_id, $chamado->setorAtendente->sg_setor, $tombos);
            }
            $usuarioId = auth()->user()->id_usuario;
            $tipoUsuario = $usuarioId == $chamado->usuario_solicitante_id ? 'Solicitante' : 'Atendente';
            ChamadoMensagem::create([
                'chamado_id' => $chamado->id_chamado,
                'mensagem' => $request->mensagem,
                'usuario_id' => $usuarioId,
                'tipo_usuario' => $tipoUsuario,
                'caminho_arquivo' => $caminhoArquivo ?? null,
            ]);
            return redirect()->back();

        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
    }

    public function ingressar(string $id)
    {
        try {
            $chamado = $this->chamadoServicos->selecionaChamadoPeloId($id);
            $usuarioId = auth()->user()->id_usuario;
            
            $chamado->update([
                'usuario_atendente_id' => $usuarioId,
                'status' => 'Em andamento',
            ]);
            
            $this->chamadoHistoricoServicos->create($chamado->toArray(), ['atividade' => 'Atendente definido']);
            
            Notificacao::mensagem('success', 'Você agora é o atendente deste chamado.');
            return redirect()->route('chamado.show',$chamado->id_chamado); 
        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
    }

    public function fechamentoChamado(Request $request, string $id)
    {
        $request->validate([
            'acao' => 'required|in:solicitar,finalizar,continuar',
        ]);

        try {
            $chamado = $this->chamadoServicos->selecionaChamadoPeloId($id);
            $usuario = auth()->user();
            $updateData = [];
            switch ($request->acao) {
                case 'solicitar':
                    $updateData['status'] = 'Solicitado fechamento';
                    $mensagem = "{$usuario->pessoa->nome} solicitou o fechamento do chamado";
                    $atividade = "Fechamento solicitado";
                    break;
                case 'finalizar':
                    $updateData['status'] = 'Concluido';
                    $updateData['concluded_at'] = now();
                    $mensagem = "Fechamento do chamado feito por {$usuario->pessoa->nome}";
                    $atividade = "Fechamento realizado";
                    break;
                case 'continuar':
                    $updateData['status'] = 'Em andamento';
                    $mensagem = "Fechamento do chamado cancelado por {$usuario->pessoa->nome}";
                    $atividade = "Fechamento cancelado";
                    break;
                default:
                    Notificacao::mensagem('error', 'Ação inválida');
                    return redirect()->back();
                }

                    $chamado->update($updateData);
                    $this->chamadoHistoricoServicos->create($chamado->toArray(), ['atividade' => $atividade]);
                    ChamadoMensagem::create([
                        'chamado_id' => $chamado->id_chamado,
                        'mensagem' => $mensagem,
                        'usuario_id' => $usuario->id_usuario,
                        'tipo_usuario' => 'Sistema',
                        'tipo_mensagem' => $atividade,
                    ]);
                    return redirect()->back();
        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

}
