<?php

namespace App\Http\Controllers;

use App\Models\Perfil;
use App\Models\SGS\Usuario;
use App\Util\Notificacao;
use App\Util\TratarExcecao;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

class UsuarioController extends Controller
{  
    protected $rota_usuarios_index="usuarios.index";
    protected $listaCategorias =
    [
            'A' => 'Aluno',
            'D' => 'Docente',
            'L' => 'Docente (parcial)',
            'E' => 'Estagiário',
            'P' => 'Prestador',
            'T' => 'Técnico',
            'B' => 'Técnico (parcial)',
            'V' => 'Visitante',
            'O' => 'Outro Servidor',
            'R' => 'Outro Servidor (parcial)',
    ];
    public function perfil(): View
    {
        return view('home.perfil');
    }

    public function index(Request $request): View{

        if(Gate::denies('usuarios-listar')){
            abort(403);
        }

        FormSaveInSession($request, 'usuarios');

        $listaPerfis = Perfil::all();

        $query = Usuario::withCategoria();


        $usuarios = $query->applyFilters($request->all())->paginate(10)->appends($request->query());

        return view('administrador.usuarios.index',[
            'usuarios' => $usuarios,
            'listaCategorias' => $this->listaCategorias,
            'listaPerfis' => $listaPerfis
        ]);
    }

    public function edit(string $id)
    {
        if(Gate::denies('usuarios-editar')){
            abort(403);
        }

        if(!$usuario = Usuario::withCategoria()->find($id)){
            Notificacao::mensagem('error', 'Usuário não encontrado.');
            return redirect()->route($this->rota_usuarios_index);
        }
        
        $listaPerfis = Perfil::all();

        return view('administrador.usuarios.edit', compact('usuario','listaPerfis'));
    }

    public function update(Request $request, string $id)
    {
        if(Gate::denies('usuarios-editar')){
            abort(403);
        }

        if(!$usuario = Usuario::withCategoria()->find($id)){
            Notificacao::mensagem('error', 'Usuário não encontrado.');
            return redirect()->route($this->rota_usuarios_index);
        }

        try {
            $request->validate([
                'perfis' => 'nullable|array',
                'perfis.*' => 'exists:perfis,id',
            ]);
            $usuario->perfis()->sync($request->input('perfis',[]));
            Notificacao::mensagem('success', 'Usuário atualizado com sucesso.');
        } catch (Exception $ex) {
            TratarExcecao::excecao($ex);
            return redirect()->back()->withInput();
        }
        return redirect()->route($this->rota_usuarios_index);
    }
}
