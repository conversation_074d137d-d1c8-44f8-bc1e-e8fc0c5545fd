<?php

namespace App\Http\Controllers;

use App\Services\SGS\UsuariosServicos;
use App\Util\TratarExcecao;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    public function __construct(private UsuariosServicos $usuarioServicos)
    {
    }

    public function index()
    {
        if (Auth::check()) {
            return redirect()->route('home.index');
        }

        return view('auth.login');
    }

    public function login(Request $request)
    {
        $credenciais = $request->validate([
            'ds_login' => 'required',
            'ds_senha' => 'required',
        ]);

        try {
            $this->usuarioServicos->Login($credenciais);

            return redirect()->route('home');
        } catch (Exception $ex) {
            TratarExcecao::excecao($ex);

            return redirect()->route('loginForm')->withInput();
        }
    }

    public function logout()
    {
        $this->usuarioServicos->Logout();

        return redirect('/');
    }
}
