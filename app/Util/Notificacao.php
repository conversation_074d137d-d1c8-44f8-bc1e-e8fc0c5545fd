<?php

namespace App\Util;

use Brian2694\Toastr\Facades\Toastr;

class Notificacao
{
    public static function mensagem(string $type = null, string $message, string $title = null, array $options = [])
    {
        if (! $options) {
            $options = [
                'positionClass' => 'toast-top-center',
                'closeButton' => false,
                'progressBar' => false,
                'timeOut' => '4000',
            ];
        }

        if (! $type) {
            $type = 'info';
        }

        $message = str_replace("\n", "<br>", $message);
        Toastr::$type($message, $title, $options);
    }
}
