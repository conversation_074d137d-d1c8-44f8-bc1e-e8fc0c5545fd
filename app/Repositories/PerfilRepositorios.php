<?php

namespace App\Repositories;

use App\Models\Perfil;
use App\Repositories\Contracts\PerfilIRepositorios;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class PerfilRepositorios implements PerfilIRepositorios
{
    public function obterTodos(): ?Collection
    {
        return Perfil::all();
    }

    public function obterPorId(int $id): ?Perfil
    {
        return Perfil::findOrFail($id);
    }

    public function excluir(int $id): void
    {
        Perfil::destroy($id);
    }

    public function salvar(array $perfil): ?Perfil
    {
        return Perfil::create($perfil);
    }

    public function alterar(int $id, array $perfilAtualizado): ?int
    {
        return Perfil::findOrFail($id)->update($perfilAtualizado);
    }

    public function obterTodosPaginado(int $quantidadeRegistro): ?LengthAwarePaginator
    {
        return Perfil::orderBy('nome')->paginate($quantidadeRegistro);
    }   

    public function sincronizarPermissoes(int $id, ?array $permissoes): void
    {
        Perfil::findOrFail($id)->permissoes()->sync($permissoes);
    }
}
