<?php

namespace App\Repositories;

use App\Models\HabilitarSetor;
use App\Repositories\Contracts\HabilitarSetoresIRepositorios;
use Illuminate\Database\Eloquent\Collection;

class HabilitarSetoresRepositorios implements HabilitarSetoresIRepositorios
{
    public function ObterTodosPorHabilitacao(bool $habilitado): ?Collection
    {
        return HabilitarSetor::with('setor')->where('habilitado', $habilitado)->get();
    }

    public function ObterPorIdSetor(int $idSetor): ?HabilitarSetor
    {
        return HabilitarSetor::with('setor')->where('setor_id', $idSetor)->first();
    }
}
