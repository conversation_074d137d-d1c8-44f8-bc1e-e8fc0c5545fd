<?php

namespace App\Repositories;

use App\Models\SGS\Setor;
use App\Repositories\Contracts\SetoresIRepositorios;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class SetoresRepositorios implements SetoresIRepositorios
{
    public function __construct(protected Setor $model)
    {
    }

    public function ObterTodos(): ?Collection
    {
        return Setor::all();
    }
    public function getSetores(string $setorId): Builder|null
    {
        $unidadeOrganizacional_Id = $this->verificarUnidadeOrganizacionalHabilitada($setorId);
        if(!$unidadeOrganizacional_Id) 
        {
            return null;
        }
        
        $setoresFilhosIds = $this->getSetoresFilhosIds($unidadeOrganizacional_Id);

        $query = $this->model->newQuery();

        return  $query->whereIn('id_setor', $setoresFilhosIds)->with('campus', 'setorSuperior', 'habilitarSetor', 'unidadeOrganizacional');
 
    }

    public function getUnidadeOrganizacional(array $unidadeOrganizacional): Builder
    {
        $query = $this->model->newQuery();

        return $query->whereIn(
                    'id_setor', 
                    DB::connection('pgsql_sgs')
                    ->table('setores as s1')
                    ->join('setores as s2', 's1.id_setor', '=', 's2.id_setorsuperior')
                    ->whereIn('s1.id_setorsuperior', $unidadeOrganizacional)
                    ->distinct()
                    ->pluck('s1.id_setor')
        )->with('campus','setorSuperior','unidadeOrganizacional', 'habilitarUnidadeOrganizacional');
    }

    public function verificarUnidadeOrganizacionalHabilitada(string|null $setorId): int|null
    {
        if(!$setorId) 
        {
            return null;
        }
        
        $setoresHabilitados = DB::connection('pgsql')
            ->table('habilitar_unidades_organizacionais')
            ->where('habilitado', true)
            ->pluck('unidade_id')->toArray();
        

        $unidadeOrganizacional = DB::connection('pgsql_sgs')->select("
            WITH RECURSIVE HierarquiaSetores AS (
                SELECT id_setor, id_setorsuperior
                FROM setores
                WHERE id_setor = :setorId
                
                UNION ALL
                
                SELECT s.id_setor, s.id_setorsuperior
                FROM setores s
                INNER JOIN HierarquiaSetores h ON s.id_setor = h.id_setorsuperior
            )
            SELECT id_setor
            FROM HierarquiaSetores
            WHERE id_setor = ANY(:setoresHabilitados)
            LIMIT 1
            ", [
                'setorId' => $setorId,
                'setoresHabilitados' => '{'. implode(',', $setoresHabilitados) . '}',
        ]);

        return $unidadeOrganizacional[0]->id_setor ?? null;
    }
    public function getSetoresFilhosIds(string|null $setorId): array
    {
        if(!$setorId) 
        {
            return [];
        }

        $resultados = DB::connection('pgsql_sgs')->select("
                WITH RECURSIVE HierarquiaSetores AS (
                    SELECT id_setor, id_setorsuperior
                    FROM setores
                    WHERE id_setorsuperior = :setorId
                    
                    UNION ALL
                    
                    SELECT s.id_setor, s.id_setorsuperior
                    FROM setores s
                    INNER JOIN HierarquiaSetores h ON s.id_setorsuperior = h.id_setor
                    
                )
                SELECT id_setor FROM HierarquiaSetores", ['setorId' => $setorId]);
        
        return collect($resultados)->pluck('id_setor')->toArray();
    }
}
