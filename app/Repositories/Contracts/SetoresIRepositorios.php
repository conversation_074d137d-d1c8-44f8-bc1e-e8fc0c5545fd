<?php

namespace App\Repositories\Contracts;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

interface SetoresIRepositorios
{
    public function ObterTodos(): ?Collection;
    public function getSetores(string $setorId): Builder|null;
    public function getUnidadeOrganizacional(array $unidadesOrganizacionais): Builder;
    public function verificarUnidadeOrganizacionalHabilitada(string|null $setorId): int|null;
    public function getSetoresFilhosIds(string $setorId): array;
}
