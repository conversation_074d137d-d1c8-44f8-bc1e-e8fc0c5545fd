<?php

namespace App\Repositories\Contracts;

use App\Models\Perfil;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface PerfilIRepositorios
{
    public function obterTodos(): ?Collection;
    public function obterTodosPaginado(int $quantidadeRegistro): ?LengthAwarePaginator;
    public function obterPorId(int $id): ?Perfil;
    public function excluir(int $id): void;
    public function salvar(array $perfil): ?Perfil;
    public function alterar(int $id, array $perfilAtualizado): ?int;     
    public function sincronizarPermissoes(int $id, ?array $permissoes): void;   
}
