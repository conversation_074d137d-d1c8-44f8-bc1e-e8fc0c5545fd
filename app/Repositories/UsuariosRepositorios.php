<?php

namespace App\Repositories;

use App\Models\SGS\Usuario;
use App\Repositories\Contracts\UsuariosIRepositorios;
use Illuminate\Database\Eloquent\Builder;

class UsuariosRepositorios implements UsuariosIRepositorios
{
    public function ObterUsuario(int|string $login): ?Usuario
    {
        return Usuario::with('pessoa')->where(function (Builder $query) use ($login) {
            $query
                ->where('ds_cpf', $login)
                ->orWhere('ds_login', $login)
                ->orWhereRelation('pessoa', 'ds_emailprincipal', $login);
        })->first();
    }
}
