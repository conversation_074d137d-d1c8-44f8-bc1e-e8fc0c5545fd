<?php

namespace App\Repositories;

use App\Models\Permissao;
use App\Repositories\Contracts\PermissaoIRepositorios;
use Illuminate\Database\Eloquent\Collection;

class PermissaoRepositorios implements PermissaoIRepositorios
{
    public function obterTodos(): ?Collection
    {
        return Permissao::orderBy('codigo')->get();
    }

    public function obterPorId(int $id): ?Permissao
    {
        return Permissao::findOrFail($id);
    }
}
