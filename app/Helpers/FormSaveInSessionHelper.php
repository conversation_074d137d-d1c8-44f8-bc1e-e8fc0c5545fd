<?php

 if (!function_exists('FormSaveInSession')) {

    function FormSaveInSession($datas, $formName, $standardValues = [])
    {
        $isRequest = $datas instanceof \Illuminate\Http\Request;
        $requestData = $isRequest ? $datas->all() : $datas;
        $limparFiltros = $isRequest ? $datas->get('limparFiltros') : ($datas['limparFiltros'] ?? null);
        $sessionKey = 'filtros_form_' . $formName;
        $sessionData = session($sessionKey);

        if ($limparFiltros === 'limpar') {
            // Resetar filtros para os valores padrão
            session([$sessionKey => $standardValues]);
            $isRequest ? $datas->replace($standardValues) : $datas = $standardValues;
            return $datas;
        }

        if (empty($requestData)) {
            // Primeiro acesso ou retorno à página
            $finalData = $sessionData ?? $standardValues;
           
        } else {
            // Atualizando dados
            $finalData = array_merge($sessionData ?? [], $requestData);
            
            // Se não for apenas mudança de página, resetar para página inicial
            if (!(isset($requestData['page']))) {
                // Definir página para o valor padrão (1 ou o que estiver em standardValues)
                $finalData['page'] = ($standardValues['page'] ?? 1);
            }
        }
        
        // Armazenar os dados atualizados na sessão
        session([$sessionKey => $finalData]);

        if ($isRequest) {
            $datas->replace($finalData);
        } else {
            $datas = $finalData;
        }

        return $datas;
    }
}
