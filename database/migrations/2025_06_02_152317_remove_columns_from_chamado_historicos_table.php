<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chamado_historicos', function (Blueprint $table) {
            $table->dropColumn('titulo');
            $table->dropColumn('descricao');
            $table->dropColumn('observacao');
            $table->dropColumn('telefone_contato');
            $table->dropColumn('caminho_arquivo');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chamado_historicos', function (Blueprint $table) {
            $table->string('titulo')->nullable();
            $table->text('descricao')->nullable();
            $table->text('observacao')->nullable();
            $table->string('telefone_contato')->nullable();
            $table->string('caminho_arquivo')->nullable();
        });
    }
};
