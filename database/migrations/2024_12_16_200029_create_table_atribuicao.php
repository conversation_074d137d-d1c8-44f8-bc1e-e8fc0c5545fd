<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('atribuicao', function (Blueprint $table) {
            $table->unsignedBigInteger('id_chamado');
            $table->unsignedBigInteger('id_responcavel');
            $table->unsignedBigInteger('nivel_responcavel');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('atribuicao');
    }
};
