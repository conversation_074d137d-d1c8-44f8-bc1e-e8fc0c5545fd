<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chamado_tombos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chamado_id')->constrained('chamados', 'id_chamado');
            $table->string('nm_tombo', 6);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chamado_tombos');
    }
};
