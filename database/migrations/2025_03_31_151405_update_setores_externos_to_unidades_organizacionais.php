<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::rename('habilitar_setores_externos', 'habilitar_unidades_organizacionais');
        
        Schema::table('habilitar_unidades_organizacionais', function ($table) {
            $table->renameColumn('setor_id', 'unidade_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('habilitar_unidades_organizacionais', function ($table) {
            $table->renameColumn('unidade_id', 'setor_id');
        });
        
        Schema::rename('habilitar_unidades_organizacionais', 'habilitar_setores_externos');
    }
};
