<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('permissoes', function (Blueprint $table) {
            $table->id();
            $table->string('codigo');
            $table->string('descricao')->nullable();
            $table->string('descricao_menu')->nullable();
            $table->string('ordem_menu')->nullable();
            $table->string('classe_menu')->nullable();
            $table->string('rota_menu')->nullable();
            $table->string('rota_adicional_menu')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permissoes');
    }
};
