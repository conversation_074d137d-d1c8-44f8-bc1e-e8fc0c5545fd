<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chamados', function (Blueprint $table) {
            $table->renameColumn('ds_titulo', 'titulo');
            $table->renameColumn('ds_descricao', 'descricao');
            $table->renameColumn('usuario_id', 'usuario_solicitante_id');
            $table->renameColumn('ds_telefone_contato', 'telefone_contato');

            $table->unsignedBigInteger('usuario_atendente_id')->after('usuario_solicitante_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chamados', function (Blueprint $table) {
            $table->renameColumn('titulo', 'ds_titulo');
            $table->renameColumn('descricao', 'ds_descricao');
            $table->renameColumn('usuario_solicitante_id', 'usuario_id');
            $table->renameColumn('telefone_contato', 'ds_telefone_contato');

            $table->dropColumn('usuario_atendente_id');
        });
    }
};
