<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chamados', function (Blueprint $table) {

            $table->id('id_chamado'); // Coluna id_chamado como primary key
            $table->foreignId('servico_id')->nullable()->constrained('servicos'); 
            $table->string('ds_titulo'); // Coluna título (varchar)
            $table->text('ds_descricao');
            $table->unsignedBigInteger('usuario_id');
            $table->unsignedBigInteger('setor_atendente_id');
            $table->unsignedBigInteger('setor_solicitante_id');
            $table->string('ds_telefone_contato');
            $table->string('caminho_arquivo')->nullable();
            $table->enum('status', ['Aberto', 'Em andamento','Parado' ,'Concluido'])->default('Aberto'); // Status do serviço 
            $table->timestamp('concluded_at')->nullable(); // Data de conclusão
            $table->timestamps(); // Campos created_at e updated_at
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chamados');
    }
};
