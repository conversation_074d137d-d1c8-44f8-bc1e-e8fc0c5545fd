<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chamado_historicos', function (Blueprint $table) {
            $table->id();
            $table->string('atividade')->nullable();
            $table->foreignId('chamado_id')->constrained('chamados', 'id_chamado');
            $table->foreignId('servico_id')->nullable()->constrained('servicos', 'id');
            $table->string('servico_nome')->nullable();
            $table->string('titulo');
            $table->text('descricao');
            $table->text('observacao')->nullable();
            $table->unsignedBigInteger('usuario_solicitante_id');
            $table->string('usuario_solicitante_nome');
            $table->unsignedBigInteger('usuario_atendente_id')->nullable();
            $table->string('usuario_atendente_nome')->nullable();
            $table->unsignedBigInteger('setor_solicitante_id');
            $table->string('setor_solicitante_nome');
            $table->unsignedBigInteger('setor_atendente_id');
            $table->string('setor_atendente_nome');
            $table->string('telefone_contato');
            $table->string('caminho_arquivo')->nullable();
            $table->enum('status', ['Aberto', 'Em andamento','Parado' ,'Concluido'])->default('Aberto');
            $table->timestamp('concluded_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chamado_historicos');
    }
};
