<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chamado_mensagens', function (Blueprint $table) {
            //Renomear a coluna e manter os dados
            $table->renameColumn('tipo_mensagem', 'tipo_usuario');

            //Criar nova coluna com o nome antigo e agora vazia
            $table->string('tipo_mensagem')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chamado_mensagens', function (Blueprint $table) {
            //Remove a coluna nova tipo_mensagem
            $table->dropColumn('tipo_mensagem');
            
            //Renomeia a coluna tipo_usuario para o seu nome antigo tipo_mensagem, preservando os dados
            $table->renameColumn('tipo_usuario', 'tipo_mensagem');
        });
    }
};
