<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('servicos', function (Blueprint $table) {
            $table->id();
            $table->string('nome');
            $table->string('descricao');
            $table->unsignedBigInteger('setor_responsavel');
            $table->foreign('setor_responsavel')->references('setor_id')->on('habilitar_setores')->onDelete('cascade');
            $table->boolean('status'); // Nova coluna status do tipo booleano
            $table->integer('criticidade'); // Nova coluna criticidade do tipo inteiro
            $table->timestamps();

        });
    }

    /**?
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('servicos');
    }
};
