<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('habilitar_setores_externos', function (Blueprint $table) {
            $table->id();
            $table->boolean('habilitado');
            $table->integer('setor_id');
            $table->text('descricao');
            $table->string('caminho_logomarca')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('habilitar_setores_externos');
    }
};
