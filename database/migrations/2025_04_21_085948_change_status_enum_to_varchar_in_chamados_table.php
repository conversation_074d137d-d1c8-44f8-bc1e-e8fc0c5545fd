<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove a restrição de status existente
        DB::statement("ALTER TABLE chamados DROP CONSTRAINT IF EXISTS chamados_status_check");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    
    }
};
