<?php

namespace Database\Seeders;


use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
class ChamadoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Insere 10 chamados aleatórios
        for ($i = 0; $i < 15; $i++) {
            DB::table('chamados')->insert([
                'titulo' => "Chamado $i",
                'descricao' => Str::random(50),
                'usuario_solicitante_id' => rand(1, 70), 
                'setor_atendente_id' => rand(1, 70), 
                'setor_solicitante_id' => rand(1, 70), 
                'telefone_contato' => '11-' . rand(900000000, 999999999),
                'status' => ['Aberto', 'Em andamento', 'Parado', 'Concluido'][array_rand(['Aberto', 'Em andamento', 'Parado', 'Concluido'])],
                'concluded_at' => rand(0, 1) ? now()->subDays(rand(1, 30)) : null,
                'created_at' => now()->subDays(rand(1, 60)),
                'updated_at' => now()->subDays(rand(1, 30)),
                
            ]);
        }
    }
}
