<?php

namespace Database\Seeders;

use App\Models\Perfil;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Permissao;

class PermissaoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void

    {
            
            #Permissões Ocultas
            Permissao::insert([
                #Permissões de chamados
                    ['codigo' => 'C001', 'descricao' => 'chamados-listar-todos', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' =>null , 'rota_adicional_menu' => ''],
                    ['codigo' => 'C002', 'descricao' => 'chamados-listar-unidade', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' =>null , 'rota_adicional_menu' => ''],
                    ['codigo' => 'C003', 'descricao' => 'chamados-listar-setor', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' =>null , 'rota_adicional_menu' => ''],
        
                #Permissções de regulação de chamado
                   ['codigo' => 'R001', 'descricao' => 'regulacao-visualizar', 'descricao_menu' => 'Regulação', 'ordem_menu' => 70, 'classe_menu' => 'fa-laptop', 'rota_menu' => 'regulacao.index' , 'rota_adicional_menu' => ''],
       


                #Permissões de usuarios
                 
                    ['codigo' => 'U001', 'descricao' => 'usuarios-listar', 'descricao_menu' => 'Usuários', 'ordem_menu' => 40, 'classe_menu' => 'fa-users', 'rota_menu' => 'usuarios.index', 'rota_adicional_menu' => ''],
                    ['codigo' => 'U002', 'descricao' => 'usuarios-editar', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' =>null, 'rota_adicional_menu' => ''],
                
        
                #Permissões de setores
                    ['codigo' => 'SE001', 'descricao' => 'setores-listar', 'descricao_menu' => 'Setores', 'ordem_menu' => 50, 'classe_menu' => 'fa-university', 'rota_menu' => 'setores.index', 'rota_adicional_menu' => 'setor'],
                    ['codigo' => 'SE002', 'descricao' => 'setores-editar', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' =>null , 'rota_adicional_menu' => ''],
                    
                #Permissões de unidades organizacionais
                    ['codigo' => 'UO001', 'descricao' => 'unidades-organizacionais-listar', 'descricao_menu' => 'Unidades Organizacionais', 'ordem_menu' => 60, 'classe_menu' => 'fa-university', 'rota_menu' => 'setores.index', 'rota_adicional_menu' => 'unidadeOrganizacional'],
                    ['codigo' => 'UO002', 'descricao' => 'unidades-organizacionais-editar', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' => null , 'rota_adicional_menu' => ''],

        
                #Permissões de servicos
                    ['codigo' => 'S001', 'descricao' => 'servicos-visualizar', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' =>null , 'rota_adicional_menu' => ''],
                    ['codigo' => 'S002', 'descricao' => 'servicos-listar', 'descricao_menu' => 'Serviços', 'ordem_menu' => 20, 'classe_menu' => 'fa-laptop', 'rota_menu' => 'servicos.index' , 'rota_adicional_menu' => ''],
                    ['codigo' => 'S003', 'descricao' => 'servicos-editar', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' =>null , 'rota_adicional_menu' => ''],
                    ['codigo' => 'S004', 'descricao' => 'servicos-criar', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' =>null , 'rota_adicional_menu' => ''],
        
                #Permissões de perfis  
                    ['codigo' => 'P001', 'descricao' => 'perfis-visualizar', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' => null , 'rota_adicional_menu' => ''],
                    ['codigo' => 'P002', 'descricao' => 'perfis-listar', 'descricao_menu' => 'Perfil', 'ordem_menu' => 30, 'classe_menu' => 'fa-id-badge', 'rota_menu' => 'perfil.index' , 'rota_adicional_menu' => ''],
                    ['codigo' => 'P003', 'descricao' => 'perfis-editar', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' => null , 'rota_adicional_menu' => ''],
                    ['codigo' => 'P004', 'descricao' => 'perfis-criar', 'descricao_menu' => null, 'ordem_menu' => null, 'classe_menu' => null, 'rota_menu' => null , 'rota_adicional_menu' => ''],
        
                    #Permissões de historico
                    ['codigo' => 'H001', 'descricao' => 'historico-listar', 'descricao_menu' => 'Histórico', 'ordem_menu' => 10, 'classe_menu' => 'fa-list', 'rota_menu' => '' , 'rota_adicional_menu' => ''],
              
                ]);

    }
}