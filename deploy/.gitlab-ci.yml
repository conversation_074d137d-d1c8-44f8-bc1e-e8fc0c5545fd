stages:
  - staging
  - sonarqube-check
    # - building

cache:
  key: "${CI_COMMIT_REF_SLUG}"

staging:
  image: php:8.1.2
  stage: staging
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - apt update -y && apt install -y openssh-client rsync
    #- bash deploy/deploy.sh

sonarqube-check:
  stage: sonarqube-check
  image:
    name: sonarsource/sonar-scanner-cli:4.7
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" # Defines the location of the analysis task cache
    GIT_DEPTH: "0" # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - git config --global user.name "John <PERSON>"
    - git config --global user.email <EMAIL>
    - git config --global --add safe.directory /builds/sti/central-atendimento
    - git checkout origin/develop
    - git merge $CI_COMMIT_SHA --no-commit --no-ff
    - sonar-scanner -X -Dsonar.host.url=$SONAR_HOST_URL -Dsonar.login=$SONAR_TOKEN  -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA  -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME -Dsonar.gitlab.project_id=$CI_PROJECT_ID -Dsonar.gitlab.unique_issue_per_inline=true  -Dsonar.gitlab.ci_merge_request_iid=$CI_MERGE_REQUEST_IID  -Dsonar.gitlab.user_token=$SETTINGS__GITLAB_ACCESS_TOKEN -Dsonar.gitlab.url=https://barauna.univasf.edu.br/ -Dsonar.gitlab.merge_request_discussion=true
  allow_failure: true
  only:
    - develop
    - merge_request
