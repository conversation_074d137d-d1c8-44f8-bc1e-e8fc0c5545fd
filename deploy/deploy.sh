#!/bin/bash
set -e

eval $(ssh-agent -s)
echo "$PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
mkdir -p ~/.ssh
chmod 700 ~/.ssh
ssh-keyscan $DEPLOY_SERVER >> ~/.ssh/known_hosts
chmod 644 ~/.ssh/known_hosts

DEPLOY_SERVER=$DEPLOY_SERVER
echo ">> Copiando arquivos para o servidor ${DEPLOY_SERVER}"

ssh ${USER_NAME}@${DEPLOY_SERVER} "cd ${DESTINATION_DIR}; if [ ! -d "vendor" ]; then doesnotexists=1; fi"
rsync -acv  -e ssh $(pwd)/${SOURCE_DIR} ${USER_NAME}@${DEPLOY_SERVER}:${DESTINATION_DIR}

# Inicialização dos serviços de produção
echo ">> Inicializando o docker-compose"
ssh  ${USER_NAME}@${DEPLOY_SERVER} "docker-compose -f ${DESTINATION_DIR}/docker-compose.prod.yaml up -d;"

# Execução de comandos essenciais para manter o projeto atualizado
echo ">> Executando os comandos de entrypoint"
ssh  ${USER_NAME}@${DEPLOY_SERVER} "docker exec central-atendimento-app bash docker-entrypoint.sh;"