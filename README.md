# Comandos
Executar o Docker.

```bash
- cp .env.example .env
- docker compose up -d
- docker exec -it central-atendimento-app bash 
```


<PERSON><PERSON> do Container da Aplicação (docker exec -it central-atendimento-app bash).
```bash
- npm install
- composer install
- npm run build
- php artisan key:generate
```

# Acesso
Pronto, o sistema poderá ser acessado na url http://localhost:8000/. O login é:

```bash
- Login: CPF cadastrado no SGS.
- Senha: Qualquer senha
```
