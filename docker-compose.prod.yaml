version: "3.9"
services:
  app:
    build:
      context: ./
      dockerfile: Dockerfile
    container_name: central-atendimento-app
    environment:
      - TZ=America/Recife
    restart: unless-stopped
    tty: true
    env_file:
      - ./.env
    working_dir: /var/www
    volumes:
      - "./:/var/www"
    networks:
      - centralatendimento-network

  nginx:
    image: nginx:1.27-alpine
    container_name: central-atendimento-nginx
    restart: unless-stopped
    env_file:
      - ./.env
    environment:
      TIMEZONE: America/Recife
    volumes:
      - "./:/var/www"
      - "./.docker/nginx/${APP_ENV}.conf:/etc/nginx/conf.d/default.conf"
      - /etc/ssl/private:/etc/nginx/certs
    expose:
      - 80
      - 443
    #      - "5173:5173"
    depends_on:
      - app
    networks:
      - centralatendimento-network

networks:
  centralatendimento-network:
    driver: bridge

volumes:
  storage-data:
