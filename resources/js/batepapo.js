function openImageModal(imagePath) {
    document.getElementById('modalImage').src = imagePath;
    const modal = new coreui.Modal(document.getElementById('imageModal'));
    modal.show();
}

document.addEventListener('DOMContentLoaded', function() {
    // Função para enviar mensagem com Enter
    const textarea = document.getElementById('mensagem');
    const form = textarea?.closest('form');

    // Só executa se o textarea e o form existirem
    if (textarea && form) {
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                form.submit();
            }
        });
    }

    // Adicionar event listeners para todas as imagens
    const modalImages = document.querySelectorAll('.image-message');
    if (modalImages.length > 0) {
        modalImages.forEach(img => {
            img.addEventListener('click', function() {
                openImageModal(this.src);
            });
        });
    }
});