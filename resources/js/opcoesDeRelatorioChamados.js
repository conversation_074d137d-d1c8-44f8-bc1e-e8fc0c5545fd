document.addEventListener('DOMContentLoaded', function() {
    // Define a data máxima como hoje
    const hoje = new Date().toISOString().split('T')[0];
    document.getElementById('dataInicio')?.setAttribute('max', hoje);
    document.getElementById('dataFim')?.setAttribute('max', hoje);
    
    const ordenarPorData = document.getElementById('ordenar_por_data');
    const ordenarPorStatus = document.getElementById('ordenar_por_status');
    const dataContainer = document.getElementById('data_container');
    const statusContainer = document.getElementById('status_container');
    const dataAbertura = document.getElementById('data_abertura');
    
    // Função para controlar visibilidade dos containers
    function toggleContainers() {
        if (ordenarPorData && ordenarPorData.checked) {
            if (dataContainer) dataContainer.style.display = 'block';
            if (statusContainer) statusContainer.style.display = 'none';
            if (dataAbertura) dataAbertura.required = true;
        } else {
            if (dataContainer) dataContainer.style.display = 'none';
            if (statusContainer) statusContainer.style.display = 'block';
            if (dataAbertura) dataAbertura.required = false;
        }
    }

    // Adiciona listeners para os radio buttons
    if (ordenarPorData) {
        ordenarPorData.addEventListener('change', toggleContainers);
    }
    
    if (ordenarPorStatus) {
        ordenarPorStatus.addEventListener('change', toggleContainers);
    }

    // Configura estado inicial
    toggleContainers();

    // Preenche data do formulário principal se existir
    const formDataAbertura = document.getElementById('chamado_abertura');
    if (formDataAbertura && formDataAbertura.value && dataAbertura) {
        dataAbertura.value = formDataAbertura.value;
    }

    // Validação do formulário
    const form = document.querySelector('form[action*="relatorio.chamados"]');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (ordenarPorData && ordenarPorData.checked && dataAbertura && !dataAbertura.value) {
                e.preventDefault();
                alert('Por favor, selecione uma data de abertura quando ordenar por data.');
            }
        });
    }

    const dataInicio = document.getElementById('dataInicio');
    
    if (ordenarPorData) {
        ordenarPorData.addEventListener('change', function() {
            if (this.checked && ordenarPorStatus) {
                ordenarPorStatus.checked = false;
            }
            verificarSelecaoOrdenacao();
        });
    }
    
    if (ordenarPorStatus) {
        ordenarPorStatus.addEventListener('change', function() {
            if (this.checked && ordenarPorData) {
                ordenarPorData.checked = false;
                document.getElementById('camposData').style.display = 'none';
            }
            verificarSelecaoOrdenacao();
        });
    }
    
    // Adiciona event listener para o campo de data de início
    if (dataInicio) {
        dataInicio.addEventListener('change', function() {
            verificarSelecaoOrdenacao();
        });
    }
    
    // Verifica o estado inicial dos checkboxes
    verificarSelecaoOrdenacao();

    const statusSelect = document.querySelector('#status');
    const ordenacaoDataDiv = document.querySelector('#ordenacao_data_div');
    const ordenacaoDataInput = document.querySelector('#ordenar_por_data');
    const ordenacaoStatusInput = document.querySelector('#ordenar_por_status');

    function atualizarOpcaoOrdenacao() {
        if (!dataAbertura || !statusSelect || !ordenacaoDataDiv) return;

        const dataAbertura = dataAbertura.value;
        const statusSelecionado = statusSelect.value;

        if (dataAbertura) {
            if (statusSelecionado === 'todos') {
                // Se tem data e status é 'todos', esconde opção de data e força ordenação por status
                ordenacaoDataDiv.style.display = 'none';
                ordenacaoStatusInput.checked = true;
            } else {
                // Se tem data e status não é 'todos', esconde opções e força ordenação por data
                ordenacaoDataDiv.style.display = 'none';
                ordenacaoDataInput.checked = true;
            }
        } else {
            // Se não tem data, mostra todas as opções
            ordenacaoDataDiv.style.display = 'block';
        }
    }

    // Monitora mudanças na data de abertura e status
    if (dataAbertura) {
        dataAbertura.addEventListener('change', atualizarOpcaoOrdenacao);
    }
    if (statusSelect) {
        statusSelect.addEventListener('change', atualizarOpcaoOrdenacao);
    }

    // Executa na carga inicial
    atualizarOpcaoOrdenacao();
});

function verificarSelecaoOrdenacao() {
    const ordenarPorData = document.getElementById('ordenar_por_data');
    const ordenarPorStatus = document.getElementById('ordenar_por_status');
    const dataInicio = document.getElementById('dataInicio');
    const btnGerarRelatorio = document.getElementById('btnGerarRelatorio');
    const erroOrdenacao = document.getElementById('erroOrdenacao');
    const camposData = document.getElementById('camposData');
    
    // Mostra/esconde os campos de data
    if (ordenarPorData && ordenarPorData.checked) {
        camposData.style.display = 'block';
    } else {
        camposData.style.display = 'none';
    }
    
    // Verifica se a seleção é válida
    let selecaoValida = false;
    
    if (ordenarPorStatus && ordenarPorStatus.checked) {
        selecaoValida = true;
    } else if (ordenarPorData && ordenarPorData.checked) {
        // Se ordenar por data está selecionado, a data de início deve estar preenchida
        selecaoValida = dataInicio && dataInicio.value;
    }
    
    // Habilita/desabilita o botão com base na seleção válida
    if (btnGerarRelatorio) {
        btnGerarRelatorio.disabled = !selecaoValida;
    }
    
    // Atualiza a mensagem de erro
    if (erroOrdenacao) {
        if (!ordenarPorData?.checked && !ordenarPorStatus?.checked) {
            erroOrdenacao.textContent = 'Selecione pelo menos uma opção de ordenação para gerar o relatório.';
            erroOrdenacao.style.display = 'block';
        } else if (ordenarPorData?.checked && !dataInicio?.value) {
            erroOrdenacao.textContent = 'Preencha a data de início para gerar o relatório.';
            erroOrdenacao.style.display = 'block';
        } else {
            erroOrdenacao.style.display = 'none';
        }
    }
}

function validarDatas() {
    const dataInicio = document.getElementById('dataInicio');
    const dataFim = document.getElementById('dataFim');
    const erroData = document.getElementById('erroData');
    const erroDataFutura = document.getElementById('erroDataFutura');
    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);
    
    let temErro = false;
    
    // Habilita o campo de data final apenas se a data inicial estiver preenchida
    if (dataInicio.value) {
        dataFim.disabled = false;
    } else {
        dataFim.disabled = true;
        dataFim.value = '';
    }
    
    // Verifica se alguma data é futura
    if (dataInicio.value && new Date(dataInicio.value) > hoje) {
        erroDataFutura.style.display = 'block';
        temErro = true;
    } else if (dataFim.value && new Date(dataFim.value) > hoje) {
        erroDataFutura.style.display = 'block';
        temErro = true;
    } else {
        erroDataFutura.style.display = 'none';
    }
    
    // Verifica se a data final é posterior à data inicial
    if (dataInicio.value && dataFim.value) {
        if (new Date(dataFim.value) < new Date(dataInicio.value)) {
            erroData.style.display = 'block';
            temErro = true;
        } else {
            erroData.style.display = 'none';
        }
    } else {
        erroData.style.display = 'none';
    }
    
    // Verifica novamente a seleção de ordenação após validar as datas
    verificarSelecaoOrdenacao();
    
    return !temErro;
}

// Exportar funções para uso global
window.verificarSelecaoOrdenacao = verificarSelecaoOrdenacao;
window.validarDatas = validarDatas;