import Chart from 'chart.js/auto';
 function doughnutChart(idGrafico = 'doughnutChart',Innerlabels,objectName, Innerdata) {  

    const ctx = document.getElementById(idGrafico).getContext('2d');
    const data = {
        //labels: ['Red', 'Blue', 'Yellow', 'Green'],
        labels: Innerlabels,
        datasets: [{
            //label: 'My Doughnut Chart',
            label: objectName,
            //data: [12, 19, 3, 5], // Valores para cada segmento
            data: Innerdata,
            backgroundColor: [
                'rgba(255, 99, 132, 0.2)',
                'rgba(54, 162, 235, 0.2)',
                'rgba(255, 206, 86, 0.2)',
                'rgba(75, 192, 192, 0.2)'
            ],
            borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)'
            ],
            borderWidth: 1
        }]
    };

    const config = {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    enabled: true,
                }
            }
        }
    };
    new Chart(ctx, config);
}

function barChart(idGrafico = 'barChart',Innerlabels, Data01, Data02) { 

    const ctx2 = document.getElementById(idGrafico).getContext('2d');
        // Dados do gráfico
        let labels = Innerlabels; // Meses
        //let data2024 = [150, 200, 180, 220, 170, 210]; // Atendimentos de 2024
        let data2024 = Data01; // Atendimentos de 2024
        //let data2025 = [160, 190, 210, 230, 180, 220]; // Atendimentos de 2025
        let data2025 = Data02; // Atendimentos de 2025

        const config2 = {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '2024',
                        data: data2024,
                        backgroundColor: 'rgba(54, 162, 235, 0.6)', // Azul
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    },
                    {
                        label: '2025',
                        data: data2025,
                        backgroundColor: 'rgba(255, 99, 132, 0.6)', // Vermelho
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        enabled: true,
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        };

        new Chart(ctx2, config2);
}

function lineChart(idGrafico = 'lineChart',Innerlabels,Data) {

    const ctx3 = document.getElementById(idGrafico).getContext('2d');

    // Dados do gráfico
     //labels = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho']; // Meses
     const labels = Innerlabels; // Meses
     //data2024 = [150, 170, 180, 220, 210, 244]; // Atendimentos de 2024
    const data2024 = Data; // Atendimentos de 2024
    const config3 = {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Atendimentos em 2024',
                    data: data2024,
                    backgroundColor: 'rgba(75, 192, 192, 0.2)', // Verde claro
                    borderColor: 'rgba(75, 192, 192, 1)', // Verde escuro
                    borderWidth: 2,
                    tension: 0.4, // Suaviza as linhas
                    fill: true // Preenche a área abaixo da linha
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    enabled: true,
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    };

    new Chart(ctx3, config3);

}


function verticalBarChart(idGrafico = 'verticalBarChart',Innerlabels,Data) {
    const ctx = document.getElementById(idGrafico).getContext('2d');

        // Configuração do gráfico
        new Chart(ctx, {
            type: 'bar',
            data: {
              //  labels: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio'], // Rótulos (eixo X)
                labels: Innerlabels, // Rótulos (eixo X)
                datasets: [{
                    label: 'Vendas em Milhares',
                   // data: [12, 19, 3, 5, 2], // Dados (eixo Y)
                    data: Data, // Dados (eixo Y)
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(153, 102, 255, 0.2)',
                        'rgba(255, 159, 64, 0.2)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            responsive: true,
            axis: 'y',
            options: {
                indexAxis: 'y',
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Tempo de Atendimento'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Meses'
                        }
                    }
                }
            }
        });
}


window.doughnutChart = doughnutChart;
window.barChart = barChart;
window.lineChart = lineChart;
window.verticalBarChart = verticalBarChart;

