document.addEventListener('DOMContentLoaded', function(){
    //Gerenciamento de servicos

    const setorSelect = document.getElementById('setorId');
    const servicoSelect = document.getElementById('servico_id');
    const form = document.getElementById('formRegulacao');

    const defaultSetorId = setorSelect.value;
    const defaultServicoId = servicoSelect.dataset.selectedServico;

    const loadServicos = (setorId, selectedServicoId) => {
        if(setorId){
            servicoSelect.innerHTML = '<option value="">Selecione um Serviço</option>';
            fetch(`/get/servicos/${setorId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            })
                .then(response => response.json())
                .then(servicos => {
                    if(servicos.length > 0){
                        servicos.forEach(servico => {
                            const option = document.createElement('option');
                            option.value = servico.id;
                            option.textContent = servico.nome;
                            if(selectedServicoId && servico.id == selectedServicoId){
                                option.selected = true;
                            }
                            servicoSelect.appendChild(option);
                        });
                    } else {
                        const option = document.createElement('option');
                        option.value = '';
                        option.textContent = 'Nenhum Serviço Disponível';
                        servicoSelect.appendChild(option);
                    }
                })
                .catch(error => console.error('Erro ao carregar servicos:', error));
        } else {
            servicoSelect.innerHTML = '<option value="">Selecione um Setor</option>';
        }
    };

    if(defaultSetorId){
        loadServicos(defaultSetorId, defaultServicoId);
    }

    setorSelect.addEventListener('change', function(){
        loadServicos(this.value);
    });

    form.addEventListener('submit', function(event) {
        if (servicoSelect.value === ''){
            event.preventDefault();
            servicoSelect.setCustomValidity('Selecione um Serviço');
            servicoSelect.reportValidity();
        } else {
            servicoSelect.setCustomValidity('');
        }
    });
});