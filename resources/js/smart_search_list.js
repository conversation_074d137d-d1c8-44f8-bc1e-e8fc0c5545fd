//JS com o objetivo de alterar de forma dinâmica a listagem de itens apresentados

// GENERAL SMARTLIST
    //Campo de busca do tipo search
    const inputSearch = document.querySelector("input[type='search']");

    //Todos os itens da lista (bloco de código)
    const content = document.getElementsByClassName("smartLisBlock");
    //Array com os itens da lista
    const arrayContent = Array.from(content);
  

//SERVICES SMARTLIST (CREATE->SETORES)
//Campo do formulário a ser preenchido
    const finalInputField = document.getElementById("finalInputField");

//SERVICES SMARTLIST (SEVICES-LIST)


//mapeamento no array de todas as siglas de setores
const smartListAttribute_01 = arrayContent.map(item => item.querySelector('.smartListAttribute_01').textContent.trim());
//mapeamento no array de todos os nomes de setores
const smartListAttribute_02 = arrayContent.map(item => item.querySelector('.smartListAttribute_02').textContent.trim());

//Seleciona todos os elementos com a classe campusLista
let selectElement = document.getElementsByClassName("smartListSelect_01")[0]; 


if(selectElement === undefined){
    selectElement = null;
}

const listaDeBusta = [...smartListAttribute_01, ...smartListAttribute_02 ];

// Função para atualizar a exibição dos itens da lista
function atualizarLista() {
    const termoPesquisado = inputSearch.value.toLowerCase(); // Valor do campo de busca

    let selectElementValue = selectElement ==null? null : selectElement.value;
   
    const resultados = listaDeBusta.filter(valor => valor.toLowerCase().includes(termoPesquisado));

    arrayContent.forEach((item, index) => {
        
        let campusItem = item.querySelector(".smartListAttribute_03") !== null? item.querySelector(".smartListAttribute_03").textContent.trim() : null;
        let matchResultado = resultados.includes(smartListAttribute_02[index]) || resultados.includes(smartListAttribute_01[index]);

        let mostrarItem = (selectElementValue === "TODOS" || selectElementValue === null)
                ? matchResultado
                : campusItem === selectElementValue && matchResultado;

        item.style.display = mostrarItem ? '' : 'none';

        
    });
}

// Evento de entrada no campo de busca
inputSearch.addEventListener("input", atualizarLista);

// Evento de mudança no select
if(selectElement !== null && selectElement !== undefined){
    selectElement.addEventListener("change", atualizarLista);
}


//Evento que torna os itens da lista clicáveis e atualiza o input do formulário
arrayContent.forEach((iten,index) => {

    iten.addEventListener("click", ()=> {
        
        finalInputField.value = smartListAttribute_02[index];                                          
    
    });
    
});
