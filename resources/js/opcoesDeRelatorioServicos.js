document.addEventListener('DOMContentLoaded', function() {
    const ordenarPorStatus = document.getElementById('ordenarPorStatus');
    const ordenarPorCriticidade = document.getElementById('ordenarPorCriticidade');
    const btnGerarRelatorio = document.getElementById('btnGerarRelatorio');
    const erroOrdenacao = document.getElementById('erroOrdenacao');

    // Adiciona listeners para os radio buttons
    if (ordenarPorStatus) {
        ordenarPorStatus.addEventListener('change', verificarSelecaoOrdenacao);
    }
    
    if (ordenarPorCriticidade) {
        ordenarPorCriticidade.addEventListener('change', verificarSelecaoOrdenacao);
    }

    // Configura estado inicial
    verificarSelecaoOrdenacao();
});

// Função para verificar a seleção e controlar o botão
window.verificarSelecaoOrdenacao = function() {
    const ordenarPorStatus = document.getElementById('ordenarPorStatus');
    const ordenarPorCriticidade = document.getElementById('ordenarPorCriticidade');
    const statusSelecionado = ordenarPorStatus && ordenarPorStatus.checked;
    const criticidadeSelecionada = ordenarPorCriticidade && ordenarPorCriticidade.checked;
    
    if (statusSelecionado || criticidadeSelecionada) {
        btnGerarRelatorio.disabled = false;
        erroOrdenacao.style.display = 'none';
    } else {
        btnGerarRelatorio.disabled = true;
        erroOrdenacao.style.display = 'block';
    }
};
