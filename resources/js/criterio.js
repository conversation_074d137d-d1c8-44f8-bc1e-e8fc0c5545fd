document.addEventListener('DOMContentLoaded', function() {
    // Elementos do DOM
    const btnAdicionarCriterio = document.getElementById('btn_adicionar_criterio');
    const novoCriterioValor = document.getElementById('novo_criterio_valor');
    const novoCriterioCampo = document.getElementById('novo_criterio_campo');
    const criteriosContainer = document.getElementById('criterios_container');
    
    // Mapeamento de campos para nomes legíveis
    const camposNomes = {
        'chamado_titulo': 'Título',
        'chamado_atendente': 'Setor atendente',
        'chamado_solicitante': 'Setor solicitante',
        'chamado_usuario_solicitante': 'Usuário solicitante'
    };
    
    // Função para adicionar um novo critério
    function adicionarCriterio() {
        const valor = novoCriterioValor.value.trim();
        const campo = novoCriterioCampo.value;
        
        if (!valor) return;
        
        // Verificar se já existe um critério para este campo e removê-lo
        const criteriosExistentes = document.querySelectorAll(`.criterio-item[data-campo="${campo}"]`);
        criteriosExistentes.forEach(criterio => criterio.remove());
        
        // Criar o elemento do badge
        const badge = document.createElement('div');
        badge.className = 'criterio-item badge bg-dark text-wrap p-2';
        badge.setAttribute('data-campo', campo);
        badge.setAttribute('data-valor', valor);
        
        // Adicionar o texto do badge
        badge.textContent = `${camposNomes[campo]}: ${valor} `;
        
        // Criar o botão de remover
        const btnRemover = document.createElement('button');
        btnRemover.type = 'button';
        btnRemover.className = 'btn-close btn-close-white ms-2 btn-remover-criterio';
        btnRemover.setAttribute('aria-label', 'Remover');
        
        // Adicionar evento para remover o critério
        btnRemover.addEventListener('click', function() {
            badge.remove();
            document.getElementById(campo).value = '';
        });
        
        // Adicionar o botão ao badge
        badge.appendChild(btnRemover);
        
        // Adicionar o badge ao container
        criteriosContainer.appendChild(badge);
        
        // Atualizar o campo oculto correspondente
        document.getElementById(campo).value = valor;
        
        // Limpar o campo de entrada
        novoCriterioValor.value = '';
    }
    
    // Adicionar critério ao clicar no botão
    if (btnAdicionarCriterio) {
        btnAdicionarCriterio.addEventListener('click', adicionarCriterio);
    }
    
    // Adicionar critério ao pressionar Enter no campo de texto
    if (novoCriterioValor) {
        novoCriterioValor.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                adicionarCriterio();
            }
        });
    }
    
    // Adicionar eventos para os botões de remover critérios existentes
    document.querySelectorAll('.btn-remover-criterio').forEach(btn => {
        btn.addEventListener('click', function() {
            const criterioItem = this.closest('.criterio-item');
            const campo = criterioItem.getAttribute('data-campo');
            criterioItem.remove();
            document.getElementById(campo).value = '';
        });
    });
});