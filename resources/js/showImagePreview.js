document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('imagem');
    const imagePreview = document.getElementById('imagemPreview');
    
    if(!imageInput || !imagePreview) return;

    // Obter imagem padrão se existir
    const defaultImage = imagePreview.getAttribute('data-default-image');
    
    // Configurar o estado inicial
    updateImagePreview(imagePreview, defaultImage);
    
    imageInput.addEventListener('change', function(event){
        const file = event.target.files[0];

        if(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                updateImagePreview(imagePreview, e.target.result);
            };
            reader.readAsDataURL(file);
        } else {
            updateImagePreview(imagePreview, defaultImage);
        }
    });
});

function updateImagePreview(imageElement, src) {
    if(src) {
        imageElement.src = src;
        imageElement.style.display = 'block';
    } else {
        imageElement.src = '';
        imageElement.style.display = 'none';
    }
}
