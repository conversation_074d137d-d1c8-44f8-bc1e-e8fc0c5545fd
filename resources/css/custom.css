/* Tela de apresentação de setores*/

.card-uniform {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%; /* Garante que todos os cards da linha tenham a mesma altura */
    width: 100%;
}

.card-uniform img {
    max-height: 300px; /* Define a altura máxima para as imagens */
    object-fit: contain; /* Mantém a proporção das imagens */
    width: 100%; /* Ajusta para ocupar toda a largura do card */
    height: 200px; /* Espaço fixo para imagens */
    display: block;
    margin: auto; /* Centraliza verticalmente e horizontalmente */
}

.card-uniform .card-body {
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* Distribui título, descrição e botão uniformemente */
    align-items: center; /* Centraliza os elementos horizontalmente */
}

.card-uniform .card-title {
    margin-bottom: 8px; /* Espaço entre o título e a descrição */
    font-size: 1.2rem; /* Ajuste do tamanho da fonte */
    text-align: center; /* Centraliza o texto */
    display: flex;
    align-items: center; /* Centraliza o texto verticalmente */
    justify-content: center; /* Centraliza o texto horizontalmente */
    min-height: 30px;
    padding: 0 10px; /* Adiciona espaçamento lateral */
}

.card-uniform .card-text {
    margin-bottom: 16px; /* Espaço entre a descrição e o botão */
    text-align: justify; /* Centraliza o texto */
    display: flex;
    align-items: center; /* Centraliza o texto verticalmente */
    justify-content: center; /* Centraliza o texto horizontalmente */
    min-height: 70px;
    padding: 0 10px; /* Adiciona espaçamento lateral */
}

.card-uniform a {
    margin-top: 10px; /* Dá um espaço entre a descrição e o botão */
    align-self: center; /* Centraliza o botão horizontalmente */
}
