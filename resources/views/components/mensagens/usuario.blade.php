@props(['mensagem', 'chamado'])

@php
    $isAtendente = $mensagem->tipo_usuario == 'Atendente';
    $isUsuarioAtual = $chamado->usuarioAtual == $mensagem->tipo_usuario;
    $styleBG = $isAtendente ? 'background-color: #3C4B64;' : '';
    $textColor = $isAtendente ? 'text-white' : 'text-muted';
    $classColor = $isAtendente ? 'text-white' : 'bg-light';
    $justifyContent = $isUsuarioAtual ? 'justify-content-end' : '';
    $icon = $isAtendente ? 'headset' : 'user';
@endphp

<div class="d-flex mb-3 {{ $justifyContent }}">
    @if (!$isUsuarioAtual)
        <div class="flex-shrink-0">
            <div class="avatar {{ $classColor }} rounded-circle p-2" style="{{ $styleBG }}">
                <i class="fas fa-{{ $icon }}"></i>
            </div>
        </div>
    @endif
    <div class="flex-grow-1" style="max-width: 80%;">
        <div class="d-flex align-items-center mb-1 {{ $justifyContent }}">
            <strong class="mx-1">{{ $mensagem->usuario->pessoa->nome }}</strong>
        </div>
        <div class="d-flex flex-column p-3 {{ $classColor }} rounded" style="{{ $styleBG }}">
            <div>
                {!! nl2br(e($mensagem->mensagem)) !!}
            </div>

            @if ($mensagem->caminho_arquivo)
                <div id="imageMessage">
                    <img src="{{ asset('storage/' . $mensagem->caminho_arquivo) }}"
                        alt="Imagem enviada" class="img-fluid mt-2 image-message"
                        style="object-fit: contain; max-height: 86px; max-width: 200px; cursor: pointer;">
                </div>  
            @endif
            <small class="{{ $textColor }} d-flex justify-content-end">{{ $mensagem->created_at->format('d/m/Y, H:i') }}</small>
        </div>
    </div>
    @if ($isUsuarioAtual)
        <div class="flex-shrink-0">
            <div class="avatar {{ $classColor }} rounded-circle p-2" style="{{ $styleBG }}">
                <i class="fas fa-{{ $icon }}"></i>
            </div>
        </div>
    @endif
</div>