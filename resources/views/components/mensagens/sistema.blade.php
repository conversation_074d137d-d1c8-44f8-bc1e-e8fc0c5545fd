@props(['mensagem', 'chamado', 'ultimaSolicitacaoFechamento'])
<div class="d-flex mb-3 justify-content-center">
    <div class="flex-grow-1" style="max-width: 60%;">
        <div class="d-flex flex-column p-3 rounded" style="background-color: #FFBC2F;">{{-- #F9B115 #D59612--}}
            <div class="d-flex align-items-center mb-1 justify-content-center">
                <strong class="mx-1">Sistema</strong>
            </div>
            <div class="text-center">
                {!! nl2br(e($mensagem->mensagem)) !!}
            </div>
            @if ($chamado->status == 'Solicitado fechamento' && $mensagem->id == $ultimaSolicitacaoFechamento)
                <form action="{{route('chamado.fechamento', $chamado->id_chamado)}}" method="POST">
                    @csrf
                    <div class="d-flex justify-content-center gap-2 my-2">
                        @if ($mensagem->usuario_id != auth()->id())
                        <button type="submit" name="acao" value="finalizar" class="btn btn-success">
                            Finalizar chamado
                        </button>
                        @endif
                        <button type="submit" name="acao" value="continuar" class="btn btn-secondary">
                            Continuar chamado
                        </button>
                    </div>
                </form>
            @endif

            <small class="text-muted d-flex justify-content-end">{{ $mensagem->created_at->format('d/m/Y, H:i') }}</small>
        </div>
    </div>
</div>