@extends('layouts.app')

@section('content')
    
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div class="col-lg-11 text-center">
                <h3 class="card-title">Regulação de Chamados</h3>
            </div>
            <div class="col-lg-1 ms-auto d-flex align-items-center justify-content-center">
                <img src="{{ asset("storage/{$unidade?->caminho_logomarca}") }}" class="card-img-right img-fluid" alt="logo" >
            </div>
        </div>
        <div class="table-responsive">
            <table class="table caption-top" aria-label="Lista de chamados para regulação">
                <thead>
                    <tr>
                        <th scope="col" class="text-left">Data</th>
                        <th scope="col" class="text-left">Titulo</th>
                        <th scope="col" class="text-left">Descrição</th>
                        <th scope="col" class="text-left">Solicitante</th>
                        <th scope="col" class="text-left">Setor</th>
                        <th scope="col" class="text-right" style="text-align: right;" >Ações</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($chamados as $chamado)
                    <tr>
                        <td class="text-left">
                            <div>{{$chamado->created_at->format('d/m/y')}}</div>
                            <small class="text-muted">({{$chamado->created_at->diffForHumans()}})</small>
                        </td>
                        <td class="text-left">{{$chamado->titulo}}</td>
                        <td class="text-left" title="{{strlen($chamado->descricao) > 128 ? $chamado->descricao : ''}}">{{ Str::limit($chamado->descricao, 128, '...') }}</td>
                        <td class="text-left">{{$chamado->usuarioSolicitante->pessoa->nome}}</td>
                        <td class="text-left">{{$chamado->setorSolicitante->ds_nomesetor}}</td>
                        <td>
                            <div class="d-flex justify-content-end">
                                <a class="btn btn-warning btn-sm" title="Definir Setor"
                                href="{{ route('regulacao.edit', $chamado->id_chamado)}}">
                                <i aria-hidden="true" class="fas fa-share"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>

            </table>
            {{$chamados->links()}}
        </div>

    </div>
@endsection