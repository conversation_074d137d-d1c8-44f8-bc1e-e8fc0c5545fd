@extends('layouts.app')

@section('content')
    
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div class="col-lg-11 col-md-10 text-center">
                <h3 class="card-title">Encamin<PERSON></h3>
            </div>
            <div class="col-lg-1 col-md-2 ms-auto d-flex align-items-center justify-content-center">
                <img src="{{ asset("storage/{$unidade->caminho_logomarca}") }}" class="card-img-right img-fluid" alt="logo" >
            </div>
        </div>
        <div class="card-body">
            {{-- Informações do usuario solicitante --}}
            <div class="row ">
                <div class="form-group col-lg-5">
                    <label for="solicitante" class="fw-semibold">Solicitante:</label>
                    <p class="form-control bg-light">{{$chamado->usuarioSolicitante->pessoa->ds_nomepessoa}}</p>
                </div>
                <div class="form-group col-lg-4">
                    <label for="email" class="fw-semibold">E-mail:</label>
                    <p class="form-control bg-light">{{$chamado->usuarioSolicitante->pessoa->ds_emailprincipal}}</p>
                </div>
                <div class="form-group col-lg-3">
                    <label for="telefone" class="fw-semibold">Telefone para contato:</label>
                    <p class="form-control bg-light">{{$chamado->telefone_contato}}</p>
                </div>
            </div>
            {{-- Informações da localização do chamado --}}

            <div class="row ">
                <div class="form-group col-lg-5">
                    <label for="setor" class="fw-semibold">Setor:</label>
                    <p class="form-control bg-light">{{$chamado->setorSolicitante->ds_nomesetor}}</p>
                </div>
                <div class="form-group col-lg-3">
                    <label for="campus" class="fw-semibold">Campus:</label>
                    <p class="form-control bg-light">{{$chamado->setorSolicitante->campus->ds_nomecampus}}</p>
                </div>
            </div>

            {{-- Informações do chamado --}}
            <div class="form-group col-lg-8">
                <label for="titulo" class="fw-semibold">Título:</label>
                <p class="form-control bg-light">{{$chamado->titulo}}</p>
            </div>
            <div class="form-group col-lg-8">
                <label for="descricao" class="fw-semibold">Descrição:</label>
                <p class="form-control bg-light" style="white-space: pre-line;">{{$chamado->descricao}}</p>
            </div>

            <form action="{{route('regulacao.update', $chamado->id_chamado)}}" id="formRegulacao" method="post" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="row mb-3">
                    <div style="width: auto;">
                        <label for="setorId" class="fw-semibold">Setor:<span class="text-danger">*</span></label>
                        <select name="setorId" id="setorId" class="form-select @error('setorId') is-invalid
                        @enderror" required>
                            <option value="" selected>Selecione um Setor</option>
                            @foreach ($setores as $setor)
                                <option value="{{$setor->setor_id}}"
                                    {{old('setorId') == $setor->setor_id ? 'selected' : '' }}
                                    >{{$setor->setor->ds_nomesetor}}</option>
                            @endforeach
                        </select>
                        @error('setorId')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    <div style="width: auto;">
                        <label for="servico_id" class="fw-semibold">Serviço:<span class="text-danger">*</span></label>
                        <select name="servico_id" id="servico_id" class="form-select @error('servico_id') is-invalid
                        @enderror" data-selected-servico="{{old('servico_id')}}" required>
                            <option value="">Selecione um Setor</option>
                        </select>
                        @error('servico_id')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="observacao" class="fw-semibold">Informação Adicional (Opcional):</label>
                    <div class="col-lg-8">
                        <textarea name="observacao" id="observacao" class="form-control @error('observacao') is-invalid
                        @enderror" rows="5">{{old('observacao')}}</textarea>
                        @error('observacao')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label for="imagem" class="col-lg-3 col-form-label fw-semibold">Imagem (Opcional):</label>
                    <div class="col-lg-6">
                        <input class="form-control @error('imagem') is-invalid
                        @enderror" type="file" name="imagem" id="imagem">
                        @error('imagem')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>
                
                <div class="mx-auto">
                    <button type="submit" class="btn btn-primary">Encaminhar Chamado</button>
                    <a href="{{ url()->previous()}}" class="btn btn-secondary">Cancelar</a>
                </div>
            </form>
        </div>

    </div>
@endsection

@push('scripts')
    @vite('resources/js/regulacao.js')
@endpush