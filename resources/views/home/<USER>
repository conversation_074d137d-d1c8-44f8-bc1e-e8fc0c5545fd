@extends('layouts.app')

@section('content')
    <div class="card mb-4">
        <div class="card-header">
             Notificações 
        </div>
  
        <div class="card-body">
            As ultimas três notificações que foram recebidas
        </div>
    </div>


    <div class="card  mb-4">
        <div class="card-header gx-3">
             <h1 class="display-6 text-center"> CHAMADOS</h1>
             <div class="w-100 text-end">
                <a href="#" class="btn btn-info btn-md ms-auto" data-bs-toggle="modal" data-bs-target="#relatorioModal"><i class="fa-solid fa-chart-line"></i> Relatório da pesquisa</a>
             </div>
        </div>
  
        <div class="card-body">
            <form action="{{route('home.index')}}" class="w-100 bg-light p-3 rounded" id="atualizaListaDeChamadosForm">
                <!-- Filtros principais em linha -->
                <div class="d-flex flex-wrap justify-content-center gap-2">
                    <select name="chamado_direcionamento" id="chamado_direcionamento" class="form-select" style="width: auto;" onchange="this.form.submit()">
                        @if (in_array('chamados-listar-todos', array_column(auth()->user()->PermissoesUsuarioLogado('ocultas'), 'descricao')))
                            <option value="todos" {{$filtro['chamado_direcionamento'] === 'todos'? 'selected' : ''}}>Todos independente de setor</option>
                        @endif
                        @if(in_array('chamados-listar-setor', array_column(auth()->user()->PermissoesUsuarioLogado('ocultas'), 'descricao')))
                            <option value="setor" {{$filtro['chamado_direcionamento'] === 'setor'? 'selected' : ''}}>Chamados do setor</option>
                        @endif
                        <option value="direcionado_usuario" {{$filtro['chamado_direcionamento'] === 'direcionado_usuario'? 'selected' : ''}}>Direcionados a mim</option>
                        <option value="criado_usuario" {{$filtro['chamado_direcionamento'] === 'criado_usuario'? 'selected' : ''}}>Criados por mim</option>
                    </select>
                    
                    <select name="chamado_status" id="chamado_status" class="form-select" style="width: auto;" onchange="this.form.submit()">
                        @if(in_array('chamados-listar-todos', array_column(auth()->user()->PermissoesUsuarioLogado('ocultas'), 'descricao')))
                        <option value="Todos" {{$filtro['chamado_status'] === 'Todos'? 'selected' : ''}}>Todos os chamados</option>
                        @endif
                        @if( $filtro['chamado_direcionamento'] === 'setor' || $filtro['chamado_direcionamento'] === 'criado_usuario' || in_array('chamados-listar-todos', array_column(auth()->user()->PermissoesUsuarioLogado('ocultas'), 'descricao')) )
                        <option value="Aberto" {{$filtro['chamado_status'] === 'Aberto'? 'selected' : ''}}>Chamados abertos</option>
                        @endif
                        

                        @if($filtro['chamado_direcionamento'] !== 'setor' || in_array('chamados-listar-todos', array_column(auth()->user()->PermissoesUsuarioLogado('ocultas'), 'descricao')) )
                            <option value="Em andamento" {{$filtro['chamado_status'] === 'Em andamento'? 'selected' : ''}}>Chamados em andamento</option>
                            <option value="Parado" {{$filtro['chamado_status'] === 'Parado'? 'selected' : ''}}>Chamados parados</option>
                            <option value="Concluido" {{$filtro['chamado_status'] === 'Concluido'? 'selected' : ''}}>Chamados concluídos</option>    
                        @endif
                        
                    </select>

                    <!-- Dropdown de serviço (condicionalmente) -->
                            @if ($servicos->count() > 0)
                            <select name="chamado_servico" id="chamado_servico" class="form-select" style="width: auto;" onchange="this.form.submit()">
                                <option value='todos'>Serviço prestado</option>
                                @foreach ($servicos as $servico)
                                    <option value={{ $servico->id }} {{ $filtro['chamado_servico'] == $servico->id ? 'selected' : ''}}>{{ $servico->nome}}</option>
                                @endforeach
                            </select>
                            @endif
                </div>

                <!-- Barra de pesquisa e data na mesma linha -->
                <div class="row mb-2">
                    <div class="col-md-8 mt-2">
                        <div class="input-group">
                            <input type="text" class="form-control" id="novo_criterio_valor" placeholder="Digite sua pesquisa aqui">
                            <select class="form-select" id="novo_criterio_campo" style="max-width: 200px;">
                                <option value="chamado_titulo">Título do chamado</option>
                                @if (in_array('chamados-listar-todos', array_column(auth()->user()->PermissoesUsuarioLogado('ocultas'), 'descricao')))
                                <option value="chamado_atendente">Setor atendente</option>
                                @endif
                                <option value="chamado_solicitante">Setor solicitante</option>
                                <option value="chamado_usuario_solicitante">Usuário solicitante</option>
                            </select>
                            <button class="btn btn-outline-dark" type="button" id="btn_adicionar_criterio">Adicionar</button>
                        </div>
                    </div>
                    <div class="col-md-4 mt-2">
                        <div class="input-group">
                            <span class="input-group-text">Data de abertura</span>
                            <input type="date" class="form-control" name="chamado_abertura" id="chamado_abertura" value="{{$filtro['chamado_abertura'] ?? ''}}">
                        </div>
                    </div>
                </div>

                <!-- Área para exibir critérios adicionados -->
                <div class="row mb-1">
                    <div class="col-md-12">
                        <div id="criterios_container" class="d-flex flex-wrap gap-2">
                            <!-- Os critérios serão adicionados aqui dinamicamente -->
                            @if(!empty($filtro['chamado_titulo']))
                            <div class="criterio-item badge bg-dark text-wrap p-2" data-campo="chamado_titulo" data-valor="{{$filtro['chamado_titulo']}}">
                                Título: {{$filtro['chamado_titulo']}}
                                <button type="button" class="btn-close btn-close-white ms-2 btn-remover-criterio" aria-label="Remover"></button>
                            </div>
                            @endif
                            
                            @if(!empty($filtro['chamado_atendente']) && in_array('chamados-listar-todos', array_column(auth()->user()->PermissoesUsuarioLogado('ocultas'), 'descricao')))
                            <div class="criterio-item badge bg-dark text-wrap p-2" data-campo="chamado_atendente" data-valor="{{$filtro['chamado_atendente']}}">
                                Setor atendente: {{$filtro['chamado_atendente']}}
                                <button type="button" class="btn-close btn-close-white ms-2 btn-remover-criterio" aria-label="Remover"></button>
                            </div>
                            @endif
                            
                            @if(!empty($filtro['chamado_solicitante']))
                            <div class="criterio-item badge bg-dark text-wrap p-2" data-campo="chamado_solicitante" data-valor="{{$filtro['chamado_solicitante']}}">
                                Setor solicitante: {{$filtro['chamado_solicitante']}}
                                <button type="button" class="btn-close btn-close-white ms-2 btn-remover-criterio" aria-label="Remover"></button>
                            </div>
                            @endif
                            
                            @if(!empty($filtro['chamado_usuario_solicitante']))
                            <div class="criterio-item badge bg-dark text-wrap p-2" data-campo="chamado_usuario_solicitante" data-valor="{{$filtro['chamado_usuario_solicitante']}}">
                                Usuário solicitante: {{$filtro['chamado_usuario_solicitante']}}
                                <button type="button" class="btn-close btn-close-white ms-2 btn-remover-criterio" aria-label="Remover"></button>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Campo de data separado -->
                <div class="row mb-3">
                    <div class="col-md-6 mx-auto">
                        <!-- Container para selecionar tipo de ordenação para chamados concluídos -->
                        <div class="input-group" id="data_abertura_container"></div>

                        <!-- Container para opções de chamados concluídos -->
                        <div id="chamados_concluidos_container" style="display: none;">
                            <!-- Opções de filtro -->
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title mb-3">Filtrar chamados concluídos</h6>
                                    <div class="d-flex flex-column gap-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="filtro_conclusao" id="ultimos_10_chamados" value="ultimos_10" {{ ($filtro['filtro_conclusao'] ?? '') === 'ultimos_10' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="ultimos_10_chamados">
                                                Últimos 10 chamados
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="filtro_conclusao" id="pesquisar_por_periodo" value="periodo" {{ ($filtro['filtro_conclusao'] ?? '') === 'periodo' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="pesquisar_por_periodo">
                                                Pesquisar por período
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Container para campos de data -->
                            <div id="periodo_datas_container" class="mt-3" style="display: {{ ($filtro['filtro_conclusao'] ?? '') === 'periodo' ? 'block' : 'none' }};">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title mb-3">Período de conclusão</h6>
                                        <div class="d-flex flex-column gap-3">
                                            <div class="input-group">
                                                <span class="input-group-text">Data Início</span>
                                                <input type="date" class="form-control" name="data_conclusao_inicio" id="data_conclusao_inicio" value="{{ $filtro['data_conclusao_inicio'] ?? '' }}">
                                            </div>
                                            <div class="input-group">
                                                <span class="input-group-text">Data Fim</span>
                                                <input type="date" class="form-control" name="data_conclusao_fim" id="data_conclusao_fim" value="{{ $filtro['data_conclusao_fim'] ?? '' }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Campos ocultos para armazenar os valores -->
                <input type="hidden" name="chamado_titulo" id="chamado_titulo" value="{{$filtro['chamado_titulo'] ?? ''}}">
                @if (in_array('chamados-listar-todos', array_column(auth()->user()->PermissoesUsuarioLogado('ocultas'), 'descricao')))
                <input type="hidden" name="chamado_atendente" id="chamado_atendente" value="{{$filtro['chamado_atendente'] ?? ''}}">
                @endif
                <input type="hidden" name="chamado_solicitante" id="chamado_solicitante" value="{{$filtro['chamado_solicitante'] ?? ''}}">
                <input type="hidden" name="chamado_usuario_solicitante" id="chamado_usuario_solicitante" value="{{$filtro['chamado_usuario_solicitante'] ?? ''}}">

               

                <!-- Ordenação dos chamados -->
                <div class="d-flex justify-content-center gap-4 mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="ordenacao" id="ordenacao_data" value="data" {{ ($filtro['ordenacao'] ?? 'data') === 'data' ? 'checked' : '' }}>
                        <label class="form-check-label" for="ordenacao_data">
                            Abertos a mais tempo primeiro
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="ordenacao" id="ordenacao_criticidade" value="criticidade" {{ ($filtro['ordenacao'] ?? 'data') === 'criticidade' ? 'checked' : '' }}>
                        <label class="form-check-label" for="ordenacao_criticidade">
                            Serviços mais críticos primeiro
                        </label>
                    </div>
                </div>

                <!-- Botões de ação -->
                <div class="d-flex justify-content-center gap-3 my-3">
                    <button class="btn btn-primary" type="submit">Atualizar pesquisa</button>
                    <button class="btn btn-secondary" type="submit" name="limparFiltros" value="limpar">Resetar Filtros</button>
                </div>

                
            </form>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr class="text-center">
                            <th scope="col" style="width: 8%;">ID</th>
                            <th scope="col" style="width: 20%;">Título</th>
                            <th scope="col" style="width: 35%;" class="d-none d-md-table-cell">Descrição</th>
                            <th scope="col" style="width: 20%;" class="d-none d-lg-table-cell">Solicitante</th>
                            <th scope="col" style="width: 17%;">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($chamados as $chamado)
                                <tr class="text-center">
                                    <th class="fw-bold" scope="row">
                                        {{ $chamado->id_chamado }}
                                    </th>
                                    <td class="text-start">
                                        <p class="fw-bold mb-1">Titulo</p>
                                        <a href="
                                            {{ route(
                                                in_array($filtro['chamado_direcionamento'], ['todos', 'setor']) ? 'home.show' : 'chamado.show', $chamado->id_chamado
                                            )}}" class="text-decoration-none"> {{ $chamado->titulo }}</a>
                                    </td>
                                    <td class="text-start d-none d-md-table-cell">
                                        <p class="fw-bold mb-1">Descrição</p>
                                        <p title="{{strlen($chamado->descricao) > 128 ? $chamado->descricao : ''}}" style="word-break: break-word;">{{ Str::limit($chamado->descricao, 128, '...') }}</p>
                                    </td>
                                    <td class="text-start d-none d-lg-table-cell">
                                        <p class="fw-bold mb-1">Solicitante</p>
                                        <p class="mb-0">{{$chamado->setorSolicitante->ds_nomesetor}}</p>
                                    </td>

                                    <td>
                                        <p class="badge text-white mb-1
                                        @switch($chamado->status)
                                            @case("Aberto")
                                                bg-secondary
                                                @break
                                            @case("Em andamento")
                                                bg-info
                                                @break
                                            @case("Parado")
                                                bg-warning
                                                @break
                                            @case("Concluido")
                                                bg-success
                                                @break
                                            @case("Solicitado fechamento")
                                                bg-danger
                                                @break
                                        @endswitch">
                                            {{$chamado->status}}
                                        </p>
                                        <p class="fw-light text-center p-0 m-0 small">{{ $chamado->status === 'Concluido' && $chamado->concluded_at
                                            ? \Carbon\Carbon::parse($chamado->concluded_at)->format('d/M/y')
                                            : $chamado->created_at->format('d/M/y')
                                        }}</p>

                                    </td>
                                </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

        </div>

        <div class="d-flex justify-content-end px-3">
            {{$chamados->appends($filtro)->links()}}
        </div>
    </div>


    <div class="card">
        <div class="card-header p-2"> Início</div>
        <div class="card-body text-center p-5 row gap-4 ">
            <div class="col-9 row ">
                <div class="row justify-content-around mb-4">
                    <div class="card col-5 p-2">
                        <h6>Chamados abertos</h6>
                        <canvas id="doughnutChart" width="100" height="100"></canvas>
                    </div>
        
                    <div class="card col-6 p-2">
                        <h6>Chamados abertos</h6>
                        <canvas id="barChart" width="100" height="80"></canvas>
                    </div>
                </div>
                <div class="row justify-content-around mb-4">
                    <div class="card col-6 p-2">
                        <h6>Chamados abertos</h6>
                        <canvas id="lineChart" width="400" height="200"></canvas>
                    </div>
        
                    <div class="card col-5 p-2">
                        <h6>Chamados</h6>
                        <canvas id="graficoDeDisco02" width="100" height="100"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-3 card">

                <div style="width: 100%; height: 400px;">
                    <canvas id="verticalBarChart"></canvas>
                </div>
            </div>
             
        </div>
    </div>


    @vite('resources/js/graficos.js')
    @vite('resources/js/opcoesDeRelatorioChamados.js')

    <script>
        // Impede submit ao pressionar Enter em qualquer input do tipo texto ou search
        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('input[type="text"], input[type="search"], input[type="date"]').forEach(function (input) {
                input.addEventListener('keydown', function (event) {
                    if (event.key === 'Enter') {
                        event.preventDefault();
                    }
                });
            });

            // Submeter formulário quando a ordenação for alterada
            document.querySelectorAll('input[name="ordenacao"]').forEach(function (radio) {
                radio.addEventListener('change', function () {
                    document.getElementById('atualizaListaDeChamadosForm').submit();
                });
            });
        });
    </script>


    <script>
      document.addEventListener("DOMContentLoaded", function () {
        
        doughnutChart(
            'doughnutChart',
            ['Red', 'Blue', 'Yellow', 'Green'],
            'Chamados aceitos',
            [20, 19, 9, 5]
        );

        doughnutChart(
            'graficoDeDisco02',
            ['Aceitos', 'Parados', 'Concluídos'],
            'Chamados',
            [20, 19, 9]
        );
        barChart(
            'barChart',
            ['janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho'],
            [151, 200, 180, 220, 170, 210],
            [160, 190, 210, 230, 180, 220]
        );
        lineChart(
            'lineChart',
            ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho'],
            [150, 170, 180, 220, 210, 244]
        );

       verticalBarChart(
            'verticalBarChart',
            ['janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho'],
            [-10,3,7,-5,-5,8],
        );
     });
    </script>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>

    <!-- Modal de Relatório -->
    <div class="modal fade" id="relatorioModal" tabindex="-1" aria-labelledby="relatorioModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="relatorioModalLabel">Opções do Relatório</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <style>
                    #data_container {
                        display: none;
                    }
                </style>
                <form action="{{ route('relatorio.chamados') }}" method="GET" target="_blank">
                    <div class="modal-body">
                        <div class="mb-4">
                            <label class="form-label fw-bold">Ordenar relatório por:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_status" value="status" checked>
                                <label class="form-check-label" for="ordenar_por_status">
                                    Status
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_data" value="data">
                                <label class="form-check-label" for="ordenar_por_data">
                                    Data
                                </label>
                            </div>
                        </div>

                        <div class="mb-3" id="status_container">
                            <label class="form-label fw-bold">Status Selecionado</label>
                            <div class="alert alert-info">
                                {{ $filtro['chamado_status'] ?? 'Todos' }}
                            </div>
                        </div>

                        <div class="mb-3" id="data_container">
                            <label for="data_abertura" class="form-label fw-bold">Data de Abertura</label>
                            <input type="date" class="form-control" id="data_abertura" name="data_inicio" value="{{ $filtro['chamado_abertura'] ?? '' }}">
                        </div>

                        <input type="hidden" name="chamado_status" value="{{ $filtro['chamado_status'] ?? 'Todos' }}">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                        <button type="submit" class="btn btn-primary">Gerar Relatório</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal de Relatório de Serviços -->
    <div class="modal fade" id="relatorioServicosModal" tabindex="-1" aria-labelledby="relatorioServicosModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="relatorioServicosModalLabel">Opções do Relatório de Serviços</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <style>
                    #servico_data_container {
                        display: none;
                    }
                </style>
                <form action="{{ route('relatorio.servicos') }}" method="GET" target="_blank">
                    <div class="modal-body">
                        <div class="mb-4">
                            <label class="form-label fw-bold">Ordenar relatório por:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_status_servico" value="status" checked>
                                <label class="form-check-label" for="ordenar_por_status_servico">
                                    Status
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_criticidade" value="criticidade">
                                <label class="form-check-label" for="ordenar_por_criticidade">
                                    Criticidade
                                </label>
                            </div>
                        </div>

                        <div class="mb-3" id="servico_status_container">
                            <label class="form-label fw-bold">Status Selecionado</label>
                            <div class="alert alert-info">
                                {{ $filtro['servico_status'] ?? 'habilitados' }}
                            </div>
                        </div>

                        <div class="mb-3" id="servico_criticidade_container">
                            <label class="form-label fw-bold">Ordenação por Criticidade</label>
                            <div class="alert alert-info">
                                Os serviços serão ordenados por nível de criticidade (do mais crítico ao menos crítico)
                            </div>
                        </div>

                        <input type="hidden" name="servico_status" value="{{ $filtro['servico_status'] ?? 'habilitados' }}">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                        <button type="submit" class="btn btn-primary">Gerar Relatório</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection


@push('scripts')
    @vite('resources/js/criterio.js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Elementos do DOM
            const statusSelect = document.getElementById('chamado_status');
            const dataAberturaContainer = document.getElementById('data_abertura_container');
            const chamadosConcluidosContainer = document.getElementById('chamados_concluidos_container');
            const periodoDatasContainer = document.getElementById('periodo_datas_container');
            const ultimos10ChamadosRadio = document.getElementById('ultimos_10_chamados');
            const pesquisarPorPeriodoRadio = document.getElementById('pesquisar_por_periodo');
            const dataConclusaoInicio = document.getElementById('data_conclusao_inicio');
            const dataConclusaoFim = document.getElementById('data_conclusao_fim');

            // Criar elemento para mensagens de erro
            const mensagemErro = document.createElement('div');
            mensagemErro.className = 'alert alert-danger mt-2';
            mensagemErro.style.display = 'none';
            periodoDatasContainer.appendChild(mensagemErro);

            // Função para validar as datas de conclusão
            function validarDatasConclusao() {
                if (dataConclusaoInicio.value && dataConclusaoFim.value) {
                    const dataInicio = new Date(dataConclusaoInicio.value);
                    const dataFim = new Date(dataConclusaoFim.value);

                    if (dataFim < dataInicio) {
                        mensagemErro.textContent = 'A data final não pode ser anterior à data inicial';
                        mensagemErro.style.display = 'block';
                        dataConclusaoFim.value = dataConclusaoInicio.value;
                    } else {
                        mensagemErro.style.display = 'none';
                    }
                }
            }

            // Adicionar evento de mudança nas datas
            dataConclusaoInicio.addEventListener('change', validarDatasConclusao);
            dataConclusaoFim.addEventListener('change', validarDatasConclusao);

            // Função para alternar entre os containers de data
            function toggleDataContainers() {
                if (statusSelect.value === 'Concluido') {
                    dataAberturaContainer.style.display = 'none';
                    chamadosConcluidosContainer.style.display = 'block';
                } else {
                    dataAberturaContainer.style.display = 'flex';
                    chamadosConcluidosContainer.style.display = 'none';
                    periodoDatasContainer.style.display = 'none';
                    mensagemErro.style.display = 'none';
                }
            }

            // Função para alternar a visibilidade dos campos de data
            function togglePeriodoDatas() {
                if (pesquisarPorPeriodoRadio.checked) {
                    periodoDatasContainer.style.display = 'block';
                } else {
                    periodoDatasContainer.style.display = 'none';
                    // Limpar os valores dos campos
                    dataConclusaoInicio.value = '';
                    dataConclusaoFim.value = '';
                    mensagemErro.style.display = 'none';
                }
            }

            // Event listeners
            statusSelect.addEventListener('change', toggleDataContainers);
            ultimos10ChamadosRadio.addEventListener('change', togglePeriodoDatas);
            pesquisarPorPeriodoRadio.addEventListener('change', togglePeriodoDatas);

            // Inicializar o estado dos campos
            toggleDataContainers();
            togglePeriodoDatas();
        });
    </script>
@endpush
