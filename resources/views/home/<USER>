@extends('layouts.app')

@section('content')
    <div class="container">
        <div class="card card-body border-0">
            <h4>Meu perfil</h4>

            <div class="card card-body my-4">
                <h6><PERSON><PERSON></h6>

                <div class="row mt-4">
                    <div class="form-group col-lg-6 col-md-8 col-sm-12 mb-sm-3">
                        <label class="fw-semibold">Nome</label>
                        <input type="text" value="{{ auth()->user()->pessoa->ds_nomepessoa }}" class="form-control bg-light"
                            readonly>
                    </div>

                    <div class="form-group col-lg-4 col-md-4">
                        <label class="fw-semibold">Siape</label>
                        <input type="text" value="{{ auth()->user()->lotacao->nm_siape }}" class="form-control bg-light"
                            readonly>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="form-group col-lg-6 col-md-8 mb-sm-3">
                        <label class="fw-semibold">E-mail</label>
                        <input type="text" value="{{ auth()->user()->pessoa->ds_emailprincipal }}"
                            class="form-control bg-light" readonly>
                    </div>

                    <div class="form-group col-lg-4 col-md-4">
                        <label class="fw-semibold">Telefone</label>
                        <input type="text" value="{{ auth()->user()->pessoa->ds_telefones }}"
                            class="form-control bg-light" readonly>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="form-group col-lg-10">
                        <label class="fw-semibold">Setor/Unidade de exercício</label>
                        <input type="text"
                            value="{{ auth()->user()->setor->ds_nomesetor }} - {{ auth()->user()->unidade_exercicio->ds_nomesetor }}"
                            class="form-control bg-light">
                    </div>
                </div>
            </div>

            <div class="card card-body my-4">
                <h6>Dados de Acesso</h6>

                <div class="card card-body">
                    <div class="align-items-center justify-content-center row">
                        <div class="form-group col-lg-4">
                            <label class="fw-semibold">Login</label>
                            <input type="text" value="{{ auth()->user()->ds_login }}" class="form-control" readonly>
                        </div>

                        <div class="form-group col-lg-4">
                            <label class="fw-semibold">Senha</label>
                            <input type="password" class="form-control">
                        </div>

                        <div class="form-group col-lg-4">
                            <label class="fw-semibold">Confirmação de Senha</label>
                            <input type="password" class="form-control">
                        </div>
                    </div>
                </div>
            </div>


            <div class="text-center">
                <button class="btn btn-primary btn-lg">Atualizar</button>
            </div>
        </div>
    </div>
@endsection
