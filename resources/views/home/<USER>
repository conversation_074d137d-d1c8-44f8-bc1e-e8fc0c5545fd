@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-header d-flex align-items-center bg-light">
            <div>
                <h2 class="mb-0">{{$chamado->titulo}}</h2>
                <small class="text-muted">Chamado #{{$chamado->id_chamado}}</small>
            </div>
            <a href="{{route('home.index')}}" class="btn btn-secondary ms-auto">
                 Voltar
            </a>
        </div>
  
        <div class="card-body">
            {{-- Status Banner --}}
            <div class="alert {{ $chamado->statusClass }} d-flex align-items-center mb-4">
                <i class="fas {{ $chamado->statusIcon }} me-2"></i>
                <div>
                    Status: <strong>{{$chamado->status}}</strong>
                </div>
            </div>

            <div class="row g-4">
                {{-- Informações do Solicitante --}}
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                Informações do Solicitante
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <strong>Nome:</strong> {{$chamado->usuarioSolicitante->pessoa->nome}}
                                </li>
                                <li class="list-group-item">
                                    <strong>Telefone:</strong> {{$chamado->telefone_contato}}
                                </li>
                                <li class="list-group-item">
                                    <strong>Email:</strong> {{$chamado->usuarioSolicitante->pessoa->ds_emailprincipal}}
                                </li>
                                <li class="list-group-item">
                                    <strong>Setor:</strong> {{$chamado->setorSolicitante->ds_nomesetor}}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                {{-- Informações do Chamado --}}
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                Informações do Chamado
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <strong>Serviço:</strong> {{$chamado->servico?->nome}}
                                </li>
                                <li class="list-group-item">
                                    <strong>Criticidade:</strong>
                                    <span class="badge {{ $chamado->servicoClass}}">
                                        {{ $chamado->servicoStatus}}
                                    </span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Criado em:</strong> {{$chamado->created_at->format('d/m/Y H:i')}}
                                </li>
                                <li class="list-group-item">
                                    <strong>Última atualização:</strong> {{$chamado->updated_at->format('d/m/Y H:i')}}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                {{-- Descrição --}}
                <div class="{{$chamado->infoAdicional ? 'col-lg-6' : 'col-lg-12'}}">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                Descrição
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="bg-light p-3 rounded">
                                {!! nl2br(e($chamado->descricao)) !!}
                            </div>
                        </div>
                    </div>
                </div>
                {{-- Informações adicionais --}}
                @if ($chamado->infoAdicional)
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    Informações Adicionais
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="bg-light p-3 rounded">
                                    {!! nl2br(e($chamado->infoAdicional)) !!}
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            {{-- Ações --}}
            @if ($chamado->status == 'Aberto')
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <form action="{{route('chamado.ingressar', $chamado->id_chamado)}}" method="POST">
                        @csrf
                        <button type="submit" class="btn btn-info me-2">
                            <i class="fas fa-sign-in-alt me-2"></i>Ingressar
                        </button>
                    </form>
                </div>
            @endif

            {{-- Navegação --}}
            <nav aria-label="Navegação entre chamados" class="mt-4">
                <ul class="pagination justify-content-end mb-0">
                    <li class="page-item">
                        <a class="page-link" href="#" aria-label="Anterior">
                            <i class="fas fa-chevron-left"></i>
                            <span class="d-none d-sm-inline-block ms-1">Anterior</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#" aria-label="Próximo">
                            <span class="d-none d-sm-inline-block me-1">Próximo</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
@endsection