@extends('layouts.guest')

@section('content')
    <div class="d-flex justify-content-center"> 
        <form action="{{ route('login') }}" method="POST">
            @csrf
            <div>
                <div class="fw-bold fs-5 mb-4">
                    Seja bem vindo
                </div>
                <div class="row">
                    <label for="ds_login" class="form-label">LOGIN</label>
                    <div class="input-group mb-3">
                        <input class="form-control @error('ds_login') is-invalid @enderror" type="text" name="ds_login"
                            required autofocus value="{{ old('ds_login') }}">
                        <span class="input-group-text">
                            <i aria-hidden="true" class="fas fa-user"></i>
                        </span>
                        @error('ds_login')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>
                <div class="mt-2 row">
                    <label for="ds_senha" class="form-label">SENHA</label>
                    <div class="input-group mb-3">
                        <input class="form-control @error('ds_senha') is-invalid @enderror" name="ds_senha" required
                            type="password">
                        <span class="input-group-text">
                            <i aria-hidden="true" class="fas fa-lock"></i>
                        </span>
                        @error('ds_senha')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>
                <div class="d-grid gap-2 mt-5">
                    <button type="submit" class="btn btn-info btn-block fw-bold text-white">ENTRAR</button>
                </div>
            </div>
        </form>
    </div>
@endsection
