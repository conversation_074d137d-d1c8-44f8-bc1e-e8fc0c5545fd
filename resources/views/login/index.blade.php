@extends('layouts.guest')

@section('content')
    <div class="col-sm-9 col-md-8 col-lg-4">
        <div class="card-group d-block d-md-flex row">
            <div class="card p-4 mb-0">
                <div class="card-body">
                    <form action="{{ route('login') }}" method="POST">
                        @csrf
                        <div class="text-center mb-5 mt-3">
                            <img  class="m-3" src="{{ asset('images/logo.png') }}" alt="Logomarca Univasf" />
                            <h5>{{ env('APP_NAME') }}</h5>
                        </div>

                        <div>
                            <label for="ds_login" class="form-label">LOGIN</label>
                            <div class="input-group mb-3">
                                <input class="form-control @error('ds_login') is-invalid @enderror" type="text"
                                    name="ds_login" id="ds_login" required autofocus value="{{ old('ds_login') }}">
                                <span class="input-group-text">
                                    <i aria-hidden="true"  class="fas fa-user"></i>
                                </span>
                                @error('ds_login')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="mt-2">
                            <label for="ds_senha" class="form-label">SENHA</label>
                            <div class="input-group mb-3">
                                <input class="form-control @error('ds_senha') is-invalid @enderror" type="password"
                                    name="ds_senha" id="ds_senha" required value="{{ old('ds_senha') }}">
                                <span class="input-group-text">
                                    <i aria-hidden="true" class="fas fa-lock"></i>
                                </span>
                                @error('ds_senha')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        </div>

                        <div class="d-grid gap-2 mt-5">
                            <button class="btn btn-info btn-block fw-bold" type="submit">LOGIN</button>
                        </div>
                        <div class="d-grid gap-2 mt-2">
                            <a href="\" class="btn btn-secondary btn-block fw-bold">CADASTRAR</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 mt-3 text-center">
        <small class="bg-light bg-opacity-50">
            © {{ date('Y') }} <a href="https://www.portais.univasf.edu.br/" target="_blank"> Universidade Federal do
                Vale do São Francisco </a> - STI
        </small>
        <br>
        @if (env('APP_ENV') != 'production')
            @include('layouts.database')
        @endif

    </div>
@endsection
