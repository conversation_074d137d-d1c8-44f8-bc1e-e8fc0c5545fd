@extends('layouts.app')

@section('content')

    @php
        $btn_visualizar = Gate::allows('servicos-visualizar')? true : false;
        $btn_editar = Gate::allows('servicos-editar')? true : false;

        if(session('filtros_form_servicos.servico_status') == null){
            session(['filtros_form_servicos.servico_status' => 'habilitados']);
        }
    @endphp

    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-between">
                <h5 class="card-title"> Lista de Serviços</h5>
                <div>
                    <a href="#" id="btnRelatorioServicos" class="btn btn-info btn-md me-2" data-bs-toggle="modal" data-bs-target="#relatorioServicosModal">
                        <i class="fa-solid fa-chart-line"></i> Relatório de Serviços
                    </a>
                    @can('servicos-criar')
                        <a href="{{route('servicos.create')}}" class="btn btn-primary">Novo</a>
                    @endcan
                </div>
            </div>
            <hr>

            
                <form id="formularioApresentar" action=" {{route('servicos.index')}}" method="get" class="w-100 d-flex row">
                    
                    <div class="row my-4 px-4">
                        
                        @if($exibirListagemSetores)
                            <select name="servico_setor" id="servico_setor" class="form-select " style="width: auto;" onchange="this.form.submit()">
                                <option value='todos'>Setor responsável</option>
                                @foreach ($setores as $id => $nome)
                                    <option value="{{ $id }}" {{ session('filtros_form_servicos.servico_setor') == $id ? 'selected' : '' }}> {{ $nome }} </option>
                                @endforeach
                            </select>
                        @endif
                    </div>
                    <div class="row">
                        <div class="col-6 d-flex justify-content-between">
                            <button class="btn  col-3  {{ session('filtros_form_servicos.servico_status') == 'habilitados'? "btn-primary" : "btn-secondary" }}" type="submit"  name='servico_status' value="habilitados">habilitados</button>
                            <button class="btn  col-3  {{ session('filtros_form_servicos.servico_status') == 'desabilitados'? "btn-primary"  : "btn-secondary" }}" type="submit"  name='servico_status' value="desabilitados">desabilitados</button>
                            <button class="btn  col-3  {{ session('filtros_form_servicos.servico_status') == 'todos'? "btn-primary" : "btn-secondary" }} "  type="submit"  name='servico_status' value="todos">Todos os serviços</button>
                        </div>

                       <div class="col-5 text-center ">
                        <input type="search" class=" text-center col-11" placeholder="Nome / setor responsável">
                       </div>

                        <button type="submit" class="btn col-1 btn-secondary" name="limparFiltros" value="limpar">Limpar</button>
                    </div>
                </form>
                @vite('resources/js/smart_search_list.js')
            </div>
            <hr>
            <table class="table caption-top">
                <thead>
                    <tr>
                        <th id=" servico_nome "class="text-left col-3">Nome</th>
                        <th id=" servico_descricao "class="text-left col-3">Descrição</th>
                        <th id=" setor_responsavel "class="text-left col-3">Setor responsável</th>
                        <th id=" servico_criticidade "class="text-left col-1">Criticidade</th>
                        <th id=" servico_status "class="text-left col-1">habilitado</th>
                       
                        <th  scope ="col" class ="col-1"></th>
                    </tr>
                </thead>
                <tbody>
                   
                   
                        
               
                    @foreach ($servicos as $servico)
                        <tr class="smartLisBlock">
                            <td class="text-left smartListAttribute_01"> {{$servico->nome}} </td>
                            <td class="text-left"> {{$servico->descricao}} </td>
                            <td class="text-left smartListAttribute_02"> {{$servico->setor_responsavel_nome}} </td>
                            <td class="text-center"> {{$servico->criticidade}}</td>
                            <td class="text-center"> 
                                @if ($servico->status)
                                    <span class="badge text-bg-success">Sim</span>
                                @else
                                    <span class="badge text-bg-danger">Nao</span>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex justify-content-end">

                                    @if ($btn_visualizar)
                                        <a class="btn btn-primary btn-sm me-2" title="Visualizar um Perfil"
                                        href= {{route('servicos.show',$servico->id)}}>
                                        <i class="fas fa-eye"></i>
                                        </a>
                                    @endif

                                    @if ($btn_editar)
                                        <a class="btn btn-warning btn-sm me-2" title="Editar um Perfil"
                                        href= {{ route('servicos.edit',$servico->id)}}>
                                        <i class="fas fa-pencil-alt"></i>
                                        </a>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforeach
                    
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modal de Relatório de Serviços -->
    <div class="modal fade" id="relatorioServicosModal" tabindex="-1" aria-labelledby="relatorioServicosModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="relatorioServicosModalLabel">Opções de Relatório de Serviços</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <form action="{{ route('relatorio.servicos') }}" method="GET">
                    @csrf
                    <div class="modal-body">
                        @if(session('filtros_form_servicos.servico_status') == 'todos')
                        <div class="mb-3" id="opcaoStatus">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" id="ordenarPorStatus" name="ordenacao" value="status" onchange="verificarSelecaoOrdenacao()">
                                <label class="form-check-label" for="ordenarPorStatus">
                                    Ordenar por status
                                </label>
                            </div>
                        </div>
                        @endif

                        <div class="mb-3" id="opcaoCriticidade">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" id="ordenarPorCriticidade" name="ordenacao" value="criticidade" onchange="verificarSelecaoOrdenacao()">
                                <label class="form-check-label" for="ordenarPorCriticidade">
                                    Ordenar por criticidade
                                </label>
                            </div>
                        </div>
                        
                        <div id="erroOrdenacao" class="text-danger" style="display: block;">
                            Selecione uma opção de ordenação para gerar o relatório.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" id="btnGerarRelatorio" class="btn btn-primary" disabled>Gerar Relatório</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Carregar Bootstrap da mesma forma que na página home -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>

   

    @vite('resources/js/smart_search_list.js')
    @vite('resources/js/opcoesDeRelatorioServicos.js')
@endsection
