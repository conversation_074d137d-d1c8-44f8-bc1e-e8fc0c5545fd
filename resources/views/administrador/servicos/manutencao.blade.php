@extends('layouts.app')

@section('content')
    <div class="card">
        
        <form method="POST" action=" {{ $rota }}">
            @csrf
           
            @if ($atualizacao)
                @method('PUT')
            @endif
            
            <div class="card-body">
                <h5 class="card-title">{{ $atividade }} Serviço</h5>
                <div class="card p-3 mt-3">
                    <div class="mb-3">
                        <label for="nome" class="form-label">Nome do serviço:<span class="text-danger">*</span></label>
                        <input type="text" required name="nome" class="form-control @error('nome') is-invalid @enderror" id="nome" value="{{ $servico?->nome }}" {{$visualizacao? 'disabled ="true"':''}}>
                             
                        @error('nome')
                            <div class="invalid-feedback">
                               {{$message}}
                            </div>
                        @enderror
                        
                    </div>

                    <div class="mb-3">
                        <label for="descricao" class="form-label">Atividade realizada:<span class="text-danger">*</span></label>
                        <textarea class="form-control @error('descricao') is-invalid @enderror" style="resize: none" name="descricao"
                            id="descricao" rows="3" {{$visualizacao? 'disabled ="true"':''}} required>{{ $servico?->descricao}}</textarea>
                         
                        @error('descricao')
                            <div class="invalid-feedback">
                                {{$message}}
                            </div>
                        @enderror
                    </div>

                  <!--  -->

                        <div class=" w-100 p-0  ">
                            <form action="#" class=" text-center w-100">

                                <div class=" text-start  ">
                                    
                                    <div class="col-4 ">
                                        <label for="finalInputField" class="form-label">Setor Responsável:<span class="text-danger">*</span></label>
                                        <input type="text" required name="finalInputField" id="finalInputField" list="listaSetores" placeholder="Setor Responsável" class=" text-center w-100  m-0 form-select @error('finalInputField') is-invalid @enderror" {{$visualizacao? 'disabled ="true"':''}}  value="{{ $servico?->setor_responsavel }}">
                                        @error('finalInputField')
                                            <div class="invalid-feedback">
                                                {{$message}}
                                            </div>
                                        @enderror
                                    </div>
                                    @if (!$visualizacao)
                                        <datalist id="listaSetores">
                                                @foreach ($setores as $setor)
                                                    <option>{{$setor->ds_nomesetor}} </option>
                                                @endforeach
                                        </datalist>

                                        <!-- Button trigger modal -->
                                        <button type="button" class="btn btn-primary  my-2" data-bs-toggle="modal" data-bs-target="#Modal">
                                            Ver todos os setores
                                        </button>
                                    @endif
                                    
                                </div>

                                <div class=" text-center d-flex align-itens-center justify-content-start w-100 my-2 flex-wrap ">
                                    <label for="" class=" w-100 h5 text-start">Criticidade:<span class="text-danger">*</span></label>
                                    <select name="criticidade" style="width: 200px;" class="form-select @error('criticidade') is-invalid @enderror" {{$visualizacao? 'disabled ="true"':''}} required>
                                        <option value=1 {{ $servico?->criticidade == 1 ? 'selected' : ''}} >Baixa</option>
                                        <option value=2 {{ $servico?->criticidade == 2 ? 'selected' : ''}} >Normal</option>
                                        <option value=3 {{ $servico?->criticidade == 3 ? 'selected' : ''}} >Importante</option>
                                        <option value=4 {{ $servico?->criticidade == 4 ? 'selected' : ''}}>Crítico</option>
                                    </select>
                                    @error('criticidade')
                                        <div class="invalid-feedback d-flex justify-content-start">
                                            {{$message}}
                                        </div>
                                    @enderror
                                </div>

                                <div class=" text-center d-flex align-itens-center justify-content-start w-100 my-2  flex-wrap">
                                    <label for="" class="w-100 h5 text-start">Estado do serviço:<span class="text-danger">*</span></label>
                                    <select name="status" style="width: 200px;" class="form-select @error('status') is-invalid @enderror" {{$visualizacao? 'disabled ="true"':''}} required>
                                        <option value= 0 {{ $servico?->status == 0 ? 'selected' : ''}}>Desabilitado</option>
                                        <option value= 1 {{ $servico?->status == 1 ? 'selected' : ''}}>Habilitado</option>

                                      </select>
                                    @error('status')
                                        <div class="invalid-feedback d-flex justify-content-start">
                                            {{$message}}
                                        </div>
                                    @enderror
                                </div>

                            </form>
                        </div>
                            

                    @if (!$visualizacao)
                    <!-- Modal -->
                    <div class="modal modal-xl " id="Modal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">

                        <div class="modal-dialog modal-dialog-scrollable">
                            <div class="modal-content">

                                <div class="modal-header">
                                    <h5 class="modal-title" id="exampleModalLabel">Setores registrados</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-header">
                                    <form action="" class="w-100 text-center d-flex align-itens-center justify-content-center">
                                        <input  type="search" placeholder="NOME OU SIGLA DO SETOR" class="col-6 text-center">
                                        <!-- ff-->
                                        <div class=" text-center d-flex align-itens-center justify-content-center col-3 p-3 flex-wrap ">
                                            
                                            <select name="campus" class="smartListSelect_01  w-100" {{$visualizacao? 'disabled ="true"':''}}>
                                                <option class="campusHabilitado"  value ="TODOS">Todos os campus</option>
                                                
                                                @foreach ($campusSetoresHabilitados as $campus)
                                                    <option class="campusHabilitado" value="{{ $campus}}" > {{$campus}} </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        
                                    </form>                                  

                                </div>

                                <div class="modal-body">

                                 <hr>
                                                                      
                                    <table class="table caption-top" aria-label="Lista de setores cadastrados">
                                        <tr>
                                            <th scope="col" class="text-left col-3">Sigla</th>
                                            <th scope="col  " class="text-left col-6">Nome do setor</th>
                                            <th scope="col" class="text-left col-3">Campus</th>
                                        </tr>
                                        
                                                                          
                                        @foreach ($setores as $setor)
                                           
                                            <tr class="smartLisBlock" style="cursor: pointer" data-bs-dismiss="modal" > 
                                                <td class="text-left  smartListAttribute_01"> {{$setor->sg_setor}} </td>
                                                <td class="text-left  smartListAttribute_02" >{{$setor->ds_nomesetor}}</td>
                                                <td class="text-left  smartListAttribute_03" >{{$setor->id_campus}}</td>
                                            </tr>
                                            
                                        @endforeach

                                        @vite('resources/js/smart_search_list.js')
                                    </table>
                                    
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Voltar</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    @endif


                    <hr>
                    <div class="mx-auto">
                        <a href="{{ url()->previous() }}" class="btn btn-secondary">{{ $visualizacao? 'Voltar':'Cancelar';}}</a>
                         
                        @if ($atualizacao)

                            <button type="submit" name='opcao' value="delete" class="btn btn-danger" form = "formularioDelete">
                                Excluir
                            </button>
                            
                        @endif
                         
                        @if (!$visualizacao)
                            <button type="submit" name='opcao' value="save" class="btn btn-primary">Salvar</button>
                       @endif 
                
                    </div>

                </div>
            </div>
        </form>

        @if ($atualizacao)
            <form id="formularioDelete" action=" {{route('servicos.destroy',$servico->id)}}" method="post">
                @csrf
                @method('DELETE')
            </form>
        @endif
    
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>
@endsection

