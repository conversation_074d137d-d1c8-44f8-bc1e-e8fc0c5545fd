@extends('layouts.app')

@section('content')
    <div class="card">
        <form method="POST" action="{{ $rota }}">
            @csrf
            @if ($atualizacao)
                @method('PUT')
            @endif

            <div class="card-body">
                <h5 class="card-title"> {{ $atualizacao ? 'Editar' : 'Cadastrar' }} Perfil</h5>
                <div class="card p-3 mt-3">
                    <div class="mb-3">
                        <label for="nome" class="form-label">Nome</label>
                        <input type="text" required name="nome"
                            class="form-control @error('nome') is-invalid @enderror" id="nome"
                            value="{{ $perfil?->nome }}">
                        @error('nome')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="descricao" class="form-label">Descrição</label>
                        <textarea class="form-control @error('descricao') is-invalid @enderror" style="resize: none" name="descricao"
                            id="descricao" rows="3">{{ $perfil?->descricao }}</textarea>
                        @error('descricao')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <div class="card p-3 mb-3">
                        <h6 class="card-title">Permissões</h6>
                        <select id="permissoes" name="permissoes[]" multiple>
                            @foreach ($permissoes as $permissao)
                                <option value="{{ $permissao->id }}" @selected($perfil?->permissoes?->contains('id', $permissao->id))>
                                    {{ $permissao->descricao }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mx-auto">
                        <a href="{{ url()->previous() }}" class="btn btn-secondary">Cancelar</a>
                        @if (!$visualizacao)
                            <button type="submit" class="btn btn-primary">Salvar</button>
                            <button type="submit" name='opcao' value="delete" class="btn btn-danger" form = "formularioDelete">
                                Excluir
                            </button>
                            
                        @endif
                    </div>

                </div>
            </div>
        </form>
        @if ($atualizacao)
            <form id="formularioDelete" action=" {{route('perfil.destroy',$perfil->id)}}" method="post">
                @csrf
                @method('DELETE')
            </form>
        @endif

    </div>
@endsection

@push('scripts')
    <script>
        $('select[name="permissoes[]"]').bootstrapDualListbox({
            selectedListLabel: 'Permissões Selecionados',
            nonSelectedListLabel: 'Permissões Não Selecionados',
            selectorMinimalHeight: 200,
            infoText: 'Mostrando {0}',
            infoTextEmpty: 'Lista Vazia',
            infoTextFiltered: '<span class="label label-warning">Filtrado</span> {0} de {1}',
            removeAllLabel: 'Remover todos',
            removeSelectedLabel: 'Remover selecionado',
            moveAllLabel: 'Mover todos',
            filterPlaceHolder: 'Filtrar',
            moveSelectedLabel: 'Mover selecionado'
        });
    </script>
@endpush
