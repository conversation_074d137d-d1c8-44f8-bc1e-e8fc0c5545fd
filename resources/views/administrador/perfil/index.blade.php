@extends('layouts.app')

@section('content')

    @php
        $btn_editar = Gate::allows('perfis-editar')? true : false;
        $btn_visualizar = Gate::allows('perfis-visualizar')? true : false;
    @endphp
    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-between">
                <h5 class="card-title"> Lista de Perfis</h5>
                @can('perfis-criar')
                    <a href="{{ route('perfil.create') }}" class="btn btn-primary">Novo</a> 
                @endcan
            </div>
            <table class="table caption-top" aria-describedby="tabela lista de perfis">
                <thead>
                    <tr>
                        <th id="Código" class="text-center col-1">Código</th>
                        <th id="Nome" class="text-left col-7">Nome</th>
                        <th id="Permissões"class="text-center col-1">Permissões</th>
                        <th scope="col" class="col-2"></th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($perfis as $perfil)
                        <tr>
                            <td class="text-center">{{ $perfil->id }}</td>
                            <td class="text-left">{{ $perfil->nome }}</td>
                            <td class="text-center">
                                <span class="badge text-bg-light">{{ $perfil->permissoes->count() }}</span>
                            </td>
                            <td>
                                <div class="d-flex justify-content-end">
                                    
                                        @if($btn_visualizar)
                                            <a class="btn btn-primary btn-sm me-2" title="Visualizar um Perfil"
                                                href={{ route('perfil.show', $perfil->id) }}>
                                                <i aria-hidden="true" class="fas fa-eye"></i>
                                            </a>
                                        @endif

                                        @if ($btn_editar) 
                                            <a class="btn btn-warning btn-sm me-2" title="Editar um Perfil"
                                                href={{ route('perfil.edit', $perfil->id) }}>
                                                <i aria-hidden="true" class="fas fa-pencil-alt"></i>
                                            </a>
                                        @endif
                                    
                                    
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            {{ $perfis->links() }}
        </div>
    </div>
@endsection
