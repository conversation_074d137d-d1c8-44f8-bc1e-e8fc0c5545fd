@extends('layouts.app')

@section('content')

<div class="card">
    <div class="card-body">
        <div class="d-flex justify-content-between">
            <h5 class="card-title"> Edição de Usuário</h5>
        </div>

        <form action="{{route('usuario.update', $usuario->id_usuario)}}" method="POST">
            @csrf
            @method('PUT')
            <fieldset disabled>
                <legend class="visually-hidden">Campos sem permissão de edição</legend>
                <div class="row">
                    <div class="form-group col-lg-6 col-md-8 col-sm-12 mb-3">
                        <label for="ds_nomepessoa" class="fw-semibold">Nome</label>
                        <input type="text" id="ds_nomepessoa" class="form-control bg-light" value="{{$usuario->ds_nomepessoa}}">
                    </div>
                    <div class="form-group col-lg-2 col-md-4 col-sm-4">
                        <label for="ds_cpf" class="fw-semibold">CPF</label>
                        <input type="text" id="ds_cpf" class="form-control bg-light" value="{{$usuario->ds_cpf}}">
                    </div>
                    <div class="form-group col-lg-2 col-md-4 col-sm-4">
                        <label for="ds_siape" class="fw-semibold">Siape</label>
                        <input type="text" id="ds_siape" class="form-control bg-light" value="{{$usuario->lotacao->nm_siape ?? ''}}">
                    </div>
                    <div class="form-group col-4 mb-3">
                        <label for="categoriaFormatada" class="fw-semibold">Categoria</label>
                        <input type="text" id="categoriaFormatada" class="form-control bg-light" value="{{$usuario->categoriaFormatada}}">
                    </div>
    
                    <div class="form-group col-lg-6 col-md-12 col-sm-12 mb-3">
                        <label for="ds_nomesetor" class="fw-semibold">Setor - Unidade de Exercício</label>
                        <input type="text" id="ds_nomesetor" class="form-control bg-light" value="{{$usuario->setor->ds_nomesetor ?? ''}} - {{$usuario->unidade_exercicio->ds_nomesetor ?? ''}}">
                    </div>
                </div>
            </fieldset>

            <div class="mb-3">
                <label for="perfis" class="fw-semibold">Perfis</label>
                <div id="perfis" class="border py-3 rounded shadow-sm bg-light">
                    <div class="form-check">
                        @foreach ($listaPerfis as $perfil)
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="perfil-{{ $perfil->id }}" name="perfis[]" value="{{ $perfil->id }}"
                                    {{ in_array($perfil->id, old( 'perfis',$usuario->perfis->pluck('id')->toArray())) ? 'checked' : '' }}>
                                <label class="form-check-label" for="perfil-{{ $perfil->id }}">
                                    {{ $perfil->nome }}
                                </label>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="mx-auto">
                <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                <a href="{{ url()->previous() }}" class="btn btn-secondary">Cancelar</a>
            </div>

        </form>

    </div>
</div>
@endsection