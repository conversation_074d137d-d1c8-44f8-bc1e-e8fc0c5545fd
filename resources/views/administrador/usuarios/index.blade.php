@extends('layouts.app')

@section('content')
    @php
        $btn_editar = Gate::allows('usuarios-editar')? true : false;
    @endphp
    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-between">
                <h5 class="card-title"> Lista de Usuários</h5>
            </div>

            <form method="GET" action="{{ route('usuarios.index') }}" class="mb-4">
                <div class="row">
                    <div class="col-lg-4 col-md-6 mb-3">
                        <input type="text" name="search" class="form-control" placeholder="Buscar por Nome ou CPF" value="{{ session('filtros_form_usuarios.search') }}">
                    </div>
                    <div class="col-lg-2 col-md-6 mb-sm-3">
                        <input type="text" name="setor" class="form-control" placeholder="Buscar por Setor" value="{{ session('filtros_form_usuarios.setor') }}">
                    </div>
                    <div class="col-lg-4 col-md-6 mb-sm-3">
                        <select name="categoria" class="form-select" onchange="this.form.submit()">
                            <option value="">Selecione uma Categoria</option>
                            @foreach ($listaCategorias as $key => $categoria)
                                <option value="{{ $key}}" {{ session('filtros_form_usuarios.categoria') == $key ? 'selected' : '' }}>{{ $categoria }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-6 mb-sm-3">
                        <select name="perfil" class="form-select" onchange="this.form.submit()">
                            <option value="">Selecione um Perfil</option>
                            @foreach ($listaPerfis as $perfil)
                                <option value="{{ $perfil->id }}" {{ session('filtros_form_usuarios.perfil') == $perfil->id ? 'selected' : '' }}>{{ $perfil->nome }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="col-4">
                        <button type="submit" class="btn btn-primary">Buscar</button>
                        <button type="submit" class="btn btn-secondary" name="limparFiltros" value="limpar">Limpar</button>

                    </div>
                </div>
            </form>
            

            <table class="table caption-top" aria-label="Lista de usuários e suas informações">
                <thead>
                    <tr>
                        <th scope="col" class="text-center col-1">CPF</th>
                        <th scope="col" class="text-left col-6">
                            <a href="{{route('usuarios.index', ['sortOrder'=> request('sortOrder', 'asc') =='asc' ? 'desc' : 'asc'] + request()->query())}}">
                                Nome
                                <i aria-hidden="true" class="fas fa-sort-{{request('sortOrder', 'asc') == 'asc' ? 'up' : 'down'}}"></i>
                            </a>
                        </th>
                        <th scope="col" class="text-center col-1">Setor</th>
                        <th scope="col" class="text-center col-1">Categoria</th>
                        <th scope="col" class="text-center col-1">Perfil</th>
                        <th scope="col" class="col-1"></th>
                    </tr>
                </thead>
                <tbody>

                    @foreach ($usuarios as $usuario)
                        <tr>
                            <td class="text-center">{{ $usuario->ds_cpf }}</td>
                            <td class="text-left">{{ $usuario->ds_nomepessoa}}</td>
                            <td class="text-center">{{$usuario->setor->sg_setor ?? ''}}</td>
                            <td class="text-center">{{$usuario->categoriaFormatada}}</td>
                            <td class="text-center">{{$usuario->perfilFormatado ?? ''}}</td>
                            <td>
                                <div class="d-flex justify-content-end">
                                    @if ($btn_editar)
                                        <a class="btn btn-warning btn-sm me-2" title="Editar um Usuario"
                                        href={{ route('usuario.edit', $usuario->id_usuario)}}>
                                        <i aria-hidden="true" class="fas fa-pencil-alt"></i>
                                        </a>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            {{ $usuarios->links() }}
        </div>
    </div>
@endsection
