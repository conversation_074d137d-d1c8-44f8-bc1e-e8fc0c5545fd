@extends('layouts.app')

@section('styles')
    @vite('resources/css/custom.css')
@endsection

@section('content')
    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-between">
                <h5 class="card-title">Acesse algum dos sistemas para ser redirecionado ao setor responsável</h5>
            </div>

            @if ($setores->count() == 0)
            <div class="alert alert-info" role="alert">
                {{ __('Nenhum setor habilitado para atendimento') }}
            </div>
            @else
                

            <div class="album p-5 bg-light">
                <div class="container">
                    <div class="row row-cols-1 row-cols-lg-3 g-4">

                        @foreach ($setores as $setor)
                            
                            <div class="col d-flex">
                                <div class="card card-uniform ">
                                    <img src="{{ asset("storage/{$setor->caminho_logomarca}") }}" 
                                        class="card-img-top p-4 img-fluid" 
                                        alt="{{$setor->setor->sg_setor}}_logo">
                                    <div class="card-body">
                                        <h5 class="card-title">{{$setor->setor->ds_nomesetor}}</h5>
                                        <p class="card-text">
                                            {{$setor->descricao}}
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            
                                            <a href="{{ route('chamado.create', ['setorAtendenteId' => $setor->setor->id_setor]) }}" 
                                                class="btn btn-primary">Acessar</a>

                                        </div>
                                    </div>

                                </div>

                            </div>
                        @endforeach


                    </div>

                </div>

            </div>

            @endif
        </div>

    </div>

@endsection