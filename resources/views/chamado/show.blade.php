@extends('layouts.app')

@section('content')
    <div class="container-fluid">
        <div class="row">
            {{-- Coluna do Chat --}}
            <div class="col-lg-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-0">{{ $chamado->titulo }}</h5>
                            <small class="text-muted">Aberto em {{ $chamado->created_at->format('d/m/Y, H:i') }}</small>
                        </div>
                        <a href="{{ route('home.index') }}" class="btn btn-secondary btn-sm">Voltar</a>
                    </div>

                    <div class="card-body">
                        {{-- Área de mensagens --}}
                        <div class="d-flex flex-column-reverse mb-4" style="height: 500px; overflow-y: auto;">
                            @foreach ($mensagens->sortByDesc('created_at') as $mensagem)
                                @if($mensagem->tipo_usuario == 'Sistema')
                                    <x-mensagens.sistema :$mensagem :$chamado :$ultimaSolicitacaoFechamento />
                                @else
                                    <x-mensagens.usuario :$mensagem :$chamado />
                                @endif
                            @endforeach
                        </div>

                        {{-- Área de input --}}
                        <div>
                            @switch($chamado->status)
                                @case('Solicitado fechamento')
                                    <div class="d-flex align-items-start alert alert-danger mb-0">
                                        <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
                                        <p class="mb-0">
                                            Não é possível enviar mensagens enquanto o chamado estiver com o fechamento solicitado, por favor responda o fechamento para continuar.
                                        </p>
                                    </div>
                                    @break
                                @case('Concluido')
                                    <div class="d-flex align-items-start alert alert-success mb-0">
                                        <i class="fas fa-check-circle me-2 mt-1"></i>
                                        <p class="mb-0">
                                            Este chamado foi concluído. Não é mais possível enviar mensagens.
                                        </p>
                                    </div>
                                    @break
                                @default
                                <form action="{{ route('chamado.storeMessage', $chamado->id_chamado) }}" method="POST"
                                    enctype="multipart/form-data" class="d-flex gap-2">
                                    @csrf
                                    <div class="d-flex flex-grow-1 align-items-start">
                                        <textarea name="mensagem" id="mensagem" class="form-control me-2 @error('mensagem') is-invalid
                                            @enderror" rows="3" placeholder="Digite sua mensagem..." required>{{old('mensagem')}}</textarea>
                                        <img id="imagemPreview" alt="Preview" class="img-fluid rounded border p-1"
                                        style="object-fit: contain; max-height: 86px; max-width: 200px; display: none; ">
                                    </div>
                                    <div class="d-flex flex-column gap-2">
                                        <input type="file" name="imagem" id="imagem" class="d-none">
                                        <label for="imagem" class="btn btn-outline-{{$errors->has('imagem') ? 'danger' : 'secondary'}}" style="cursor: pointer;">
                                            <i class="fas fa-paperclip "></i>
                                        </label>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i>
                                        </button>
                                    </div>
                                </form>
                                @error('mensagem')
                                <div class="invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                                @enderror
                                @error('imagem')
                                    <div class="invalid-feedback d-block">
                                        {{ $message }} <br>Tamanho max: 1mb. <br>Formatos aceitos: .jpg, .jpeg, .png
                                    </div>
                                @enderror    
                            @endswitch
                        </div>

                    </div>
                </div>
            </div>
            {{-- Coluna de Detalhes --}}
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Detalhes do Chamado</h5>
                    </div>
                    <div class="card-body">
                        <div>
                            <label class="fw-semibold">Número do chamado</label>
                            <p class="mb-2">{{ $chamado->id_chamado }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Solicitante</label>
                            <p class="mb-2">{{ $chamado->usuarioSolicitante->pessoa->nome }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Setor Solicitante</label>
                            <p class="mb-2">{{ $chamado->setorSolicitante->ds_nomesetor }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Atendente</label>
                            <p class="mb-2">{{ $chamado->usuarioAtendente?->pessoa->nome }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Setor Atendente</label>
                            <p class="mb-2">{{ $chamado->setorAtendente->ds_nomesetor }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Status</label>
                            <div class="mb-1">
                                <span class="badge {{ $chamado->statusClass }}">{{ $chamado->status }}</span>
                            </div>
                        </div>

                        <div>
                            <label class="fw-semibold">Serviço</label>
                            <p class="mb-2">{{ $chamado->servico?->nome }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Criado em</label>
                            <p class="mb-2">{{ $chamado->created_at->format('d/m/Y, H:i') }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Última atualização</label>
                            <p class="mb-2">{{ $chamado->updated_at->format('d/m/Y, H:i') }}</p>
                        </div>
                        @if ($chamado->status != 'Concluido' && $chamado->status != 'Solicitado fechamento')
                            <form action="{{ route('chamado.fechamento', $chamado->id_chamado) }}" method="POST">
                                @csrf
                                <button type="submit" name="acao" value="{{$chamado->usuarioAtual == 'Solicitante' ? 'finalizar' : 'solicitar'}}" class="btn btn-primary w-100">Finalizar chamado</button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Modal de Imagem --}}
    <div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title fw-semibold">Imagem</h5>
                    <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body d-flex justify-content-center">
                    <img id="modalImage" src="" alt="Imagem enviada" class="img-fluid" 
                        style="object-fit: contain; max-height: 600px; max-width: 600px;">
                </div>
            </div>
        </div>
    </div>

@endsection

@push('scripts')
    @vite('resources/js/showImagePreview.js')
    @vite('resources/js/batepapo.js')
@endpush
