<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Relatório de Chamados </title>

    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        header{
            border-bottom: 2px solid #444;
            margin-bottom: 20px;
            padding-bottom: 10px;
            text-align: center;
        }
        header p{
            margin-top: 10px;
            font-size: 12px;
        }
        header  h1 {
            color: #333;
            font-size: 24px;
        }
        main article{
            margin-bottom: 30px;
        }
        main article h2{
            color: #555;
            font-size: 18px;
            margin-top: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        .indices{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .indices th, .indices td{
            border: 1px solid #ccc;
            padding: 6px;
            text-align: left;
        }
        .indices th{
            font-size: 12px;
            text-align: center;
            background-color: #f8f8f8;
            width: 30%;
        }
        .indices td{
            padding: 8px;
            font-size: 14px;
        }

        .estatisticas{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .estatisticas th, .estatisticas td{
            border: 1px solid #ccc;
            padding: 8px;
            font-size: 12px;
            vertical-align: top;
        }
        .estatisticas th{
            background-color: #f8f8f8;
            font-weight: bold;
        }
        .estatisticas td{
            line-height: 1.4;
        }
        
        hr {
            margin: 20px 0;
            border: 0;
            border-top: 1px solid #eee;
        }
        footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #777;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
    </style>

</head>
<body>

    
    <img src="{{ public_path('images/logo.png') }}" class="logo" alt="Logo">
    <header>
        <h1>Relatório de chamados</h1>
        <p>Gerado em: {{ $dataGeracao }}</p>
    </header>

    <main>
   
        <!--Indices da pesquisa -->
        <article>
            <h2>Índices de consulta</h2>
            
            <table class="indices">
                
                 @foreach ($indices as $indice => $value )
                    <tr>
                        <th id = ' indice_{{ $indice }}' >{{ $indice}}</th>
                        <td>{{ $value }}</td>  
                    </tr>
                 @endforeach
            </table>
           
        </article>

        <article>
            <!--Estatisticas gerais -->
            <h2>Estatísticas gerais</h2>
            <table class="estatisticas">
                <tr>
                    <th id = ' titulo_especificacoes' >Chamados com as especificações citadas</th>
                    <th id = ' titulo_equivalencia' >Equivalência dos chamados do sistema</th>
                </tr>
                <tr>
                    <td>{{ $estatisticasGerais["chamados_com_especificacoes"] }}</td>
                    <td>{{ number_format($estatisticasGerais["porcentagem"], 2, ',', '.') }}%</td>
                </tr>
            </table>
        </article>

        @if($ordenacao == "status")
        <article>
            <!--Dados divididos por status -->
            <h2>Estatísticas por status</h2>
            
            <!-- Estatísticas por status -->
            @foreach($estatisticasEspecificas as $status => $dados)
                @if($dados['quantidade'] > 0)
                    
                    <table class="estatisticas">
                        <tr>
                            <th id = ' status_chamado'  style="text-align: center">Status do chamado </th>
                            <th id = ' quantidade_status'  style="text-align: center">Quantidade</th>
                            <th id = ' percentual_status'  style="text-align: center">Percentual em relação ao total </th>
                        </tr>
                        
                        <tr>
                            <td>{{ strtoupper($status) }}</td>
                            <td>{{ $dados['quantidade'] }}</td>
                            <td>{{ number_format($dados['percentual'], 2, ',', '.') }}%</td>
                        </tr>
                    </table>

                    @if(count($dados['chamados']) > 0)
                        <table class="estatisticas">
                            <tr>
                                <th id = ' data_abertura' style="width: 15%">Data de abertura</th>
                                <th id = ' titulo_chamado' style="width: 25%">Título do chamado</th>
                                <th id = ' descricao_chamado' style="width: 60%">Descrição</th>
                            </tr>
                            @foreach($dados['chamados'] as $chamado)
                                <tr>
                                    <td>{{ $chamado['data_criacao'] }}</td>
                                    <td>{{ $chamado['titulo'] }}</td>
                                    <td style="text-align: left;">{{ $chamado['descricao'] }}</td>
                                </tr>
                            @endforeach
                        </table>
                    @endif
                    <hr>
                @endif
            @endforeach
        </article>
        @endif

        @if ($ordenacao == "data")
        <article>
            <!--Dados divididos por data -->
            <h2>Estatísticas por período</h2>
            
            @if(isset($semChamadosNoPeriodo) && $semChamadosNoPeriodo)
                <div class="alerta">
                    <p>Não foram encontrados chamados no período especificado.</p>
                </div>
            @elseif(count($estatisticasEspecificas) == 0)
                <div class="alerta">
                    <p>Não foram encontrados chamados no período especificado.</p>
                </div>
            @else
                <!-- Estatísticas por período (semana ou mês) -->
                @foreach($estatisticasEspecificas as $periodo => $dados)
                    @if($dados['quantidade'] > 0)
                        <table class="estatisticas">
                            <tr>
                                <th id = ' periodo_chamado' style="text-align: center">Período</th>
                                <th id = ' quantidade_periodo' style="text-align: center">Quantidade</th>
                                <th id = ' percentual_periodo' style="text-align: center">Percentual em relação ao total</th>
                            </tr>
                            <tr>
                                <td>{{ $periodo }}</td>
                                <td>{{ $dados['quantidade'] }}</td>
                                <td>{{ number_format($dados['percentual'], 2, ',', '.') }}%</td>
                            </tr>
                        </table>

                        @if(count($dados['chamados']) > 0)
                            <table class="estatisticas">
                                <tr>
                                    <th id = ' data_abertura' style="width: 15%">Data de abertura</th>
                                    <th id = ' titulo_chamado' style="width: 20%">Título do chamado</th>
                                    <th id = ' descricao_chamado' style="width: 55%">Descrição</th>
                                    <th id = ' status_chamado' style="width: 10%">Status</th>
                                </tr>
                                @foreach($dados['chamados'] as $chamado)
                                    <tr>
                                        <td>{{ $chamado['data_criacao'] }}</td>
                                        <td>{{ $chamado['titulo'] }}</td>
                                        <td style="text-align: left;">{{ $chamado['descricao'] }}</td>
                                        <td>{{ $chamado['status'] }}</td>
                                    </tr>
                                @endforeach
                            </table>
                        @endif
                        <hr>
                    @endif
                @endforeach
            @endif
        </article>
        @endif
    </main>


    <footer>
        <p>© {{ date('Y') }} Universidade Federal do Vale do São Francisco - STI</p>
        <p>Este relatório foi gerado automaticamente pelo sistema Central de Atendimento</p>
    </footer>

</body>
</html>
