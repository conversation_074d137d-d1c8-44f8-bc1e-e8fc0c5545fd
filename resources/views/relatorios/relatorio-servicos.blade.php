<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Serviços</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        header {
            text-align: center;
            margin-bottom: 30px;
        }
        header p{
            font-size: 12px;
        }
        h1 {
            color: #333;
            font-size: 24px;
        }
        h2 {
            color: #555;
            font-size: 18px;
            margin-top: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        h3 {
            color: #666;
            font-size: 16px;
            margin-top: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table.indices {
            width: 80%;
            margin: 0 auto;
        }
        table.estatisticas {
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #777;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .page-break {
            page-break-after: always;
        }
        .status-habilitado {
            color: green;
        }
        .status-desabilitado {
            color: red;
        }
    </style>
</head>
<body>

    <img src="{{ public_path('images/logo.png') }}" class="logo" alt="Logo">
    <header>
        <h1>Relatório de Serviços</h1>
        <p>Gerado em: {{ $dataGeracao }}</p>
    </header>

    <main>
        <!--Indices da pesquisa -->
        <article>
            <h2>Índices de consulta</h2>
            
            <table class="indices">
                @foreach($indices as $indice => $valor)
                <tr>
                    <th id = 'indice {{ $indice }}'>{{ $indice }}</th>
                    <td>{{ $valor }}</td>  
                </tr>
                @endforeach
            </table>
        </article>

        <article>
            <!--Estatisticas gerais -->
            <h2>Estatísticas gerais</h2>
            <table class="estatisticas">
                <tr>
                    <th id = 'titulo_especificacoes'>Serviços com as especificações citadas</th>
                    <th id = 'titulo_equivalencia'>Equivalência dos serviços do sistema</th>
                </tr>
                <tr>
                    <td>{{ $estatisticasGerais['total_filtrados'] }}</td>
                    <td>{{ number_format($estatisticasGerais['porcentagem'], 2, ',', '.') }}%</td>
                </tr>
            </table>
        </article>

        @if($ordenacao == "status")
        <article>
            <!--Dados divididos por status -->
            <h2>Estatísticas por status</h2>
            
            @if(isset($dadosOrdenados['habilitados']) && $dadosOrdenados['habilitados']['quantidade'] > 0)
            <h3>Habilitados ({{ $dadosOrdenados['habilitados']['quantidade'] }} serviços - {{ number_format($dadosOrdenados['habilitados']['percentual'], 2, ',', '.') }}%)</h3>
            <table class="estatisticas">
                <tr>
                    <th id = 'servico_nome'>Nome do serviço</th>
                    <th id = 'servico_descricao'>Descrição</th>
                    <th id = 'servico_criticidade'>Criticidade</th>
                    <th id = 'servico_total_chamados'>Total de chamados</th>
                    <th id = 'servico_tempo_medio'>Tempo médio (dias)</th>
                    <th id = 'satisfacao_media'>Satisfação média</th>
                </tr>
                @foreach($dadosOrdenados['habilitados']['servicos'] as $servico)
                <tr>
                    <td>{{ $servico->nome }}</td>
                    <td>{{ $servico->descricao }}</td>
                    <td>{{ $servico->criticidade }}</td>
                    <td>{{ $servico->total_chamados }}</td>
                    <td>{{ $servico->tempo_medio }}</td>
                    <td>{{ $servico->satisfacao_media }}</td>
                </tr>
                @endforeach
            </table>
            <hr>
            @endif
            
            @if(isset($dadosOrdenados['desabilitados']) && $dadosOrdenados['desabilitados']['quantidade'] > 0)
            <h3>Desabilitados ({{ $dadosOrdenados['desabilitados']['quantidade'] }} serviços - {{ number_format($dadosOrdenados['desabilitados']['percentual'], 2, ',', '.') }}%)</h3>
            <table class="estatisticas">
                <tr>
                    <th id = ' desabilitado_servico_nome'>Nome do serviço</th>
                    <th id = ' desabilitado_servico_descricao'>Descrição</th>
                    <th id = ' desabilitado_servico_criticidade'>Criticidade</th>
                    <th id = ' desabilitado_servico_total_chamados'>Total de chamados</th>
                    <th id = ' desabilitado_servico_tempo_medio'>Tempo médio (dias)</th>
                    <th id = ' desabilitado_servico_satisfacao_media'>Satisfação média</th>
                </tr>
                @foreach($dadosOrdenados['desabilitados']['servicos'] as $servico)
                <tr>
                    <td>{{ $servico->nome }}</td>
                    <td>{{ $servico->descricao }}</td>
                    <td>{{ $servico->criticidade }}</td>
                    <td>{{ $servico->total_chamados }}</td>
                    <td>{{ $servico->tempo_medio }}</td>
                    <td>{{ $servico->satisfacao_media }}</td>
                </tr>
                @endforeach
            </table>
            @endif
        </article>
        @endif

        @if($ordenacao == "criticidade")
        <article>
            <!--Dados divididos por criticidade -->
            <h2>Estatísticas por criticidade</h2>
            
            @php
                // Garantir que as criticidades sejam exibidas na ordem correta
                $ordemCriticidade = ['Crítico', 'Importante', 'Normal', 'Baixa'];
            @endphp
            
            @foreach($ordemCriticidade as $criticidade)
                @if(isset($dadosOrdenados[$criticidade]) && $dadosOrdenados[$criticidade]['quantidade'] > 0)
                    <h3>{{ $criticidade }} ({{ $dadosOrdenados[$criticidade]['quantidade'] }} serviços - {{ number_format($dadosOrdenados[$criticidade]['percentual'], 2, ',', '.') }}%)</h3>
                    
                    <table class="estatisticas">
                        <tr>
                            <th id = ' servico_nome'>Nome do serviço</th>
                            <th id = ' servico_descricao'>Descrição</th>
                            <th id = ' servico_status'>Status</th>
                            <th id = ' servico_total_chamados'>Total de chamados</th>
                            <th id = ' servico_tempo_medio'>Tempo médio (dias)</th>
                            <th id = ' servico_satisfacao_media'>Satisfação média</th>
                        </tr>
                        @foreach($dadosOrdenados[$criticidade]['servicos'] as $servico)
                        <tr>
                            <td>{{ $servico->nome }}</td>
                            <td>{{ $servico->descricao }}</td>
                            <td class="{{ $servico->status ? 'status-habilitado' : 'status-desabilitado' }}">
                                {{ $servico->status ? 'Habilitado' : 'Desabilitado' }}
                            </td>
                            <td>{{ $servico->total_chamados }}</td>
                            <td>{{ $servico->tempo_medio }}</td>
                            <td>{{ $servico->satisfacao_media }}</td>
                        </tr>
                        @endforeach
                    </table>
                    <hr>
                @endif
            @endforeach
        </article>
        @endif
    </main>

    <footer>
        <p>© {{ date('Y') }} Universidade Federal do Vale do São Francisco - STI</p>
        <p>Este relatório foi gerado automaticamente pelo sistema Central de Atendimento</p>
    </footer>
</body>
</html>
