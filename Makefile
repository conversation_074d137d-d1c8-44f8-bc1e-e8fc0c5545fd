setup:
	cp .env.example .env
	chmod -R a+w .env
	chmod -R a+w storage/
	chmod -R a+w bootstrap/cache/
	docker compose up -d
	docker compose exec app sh -c 'composer install --ignore-platform-reqs --no-scripts && php artisan key:generate && php artisan storage:link && npm install && npm run build'

up:
	docker compose -f docker-compose.yaml -f .docker/docker-compose.mail.yaml up -d

start:
	docker compose -f docker-compose.yaml -f .docker/docker-compose.mail.yaml start

down:
	docker compose -f docker-compose.yaml -f .docker/docker-compose.mail.yaml down

migrateup:
	docker compose exec app bash -c "php artisan migrate --seed"

test:
	docker compose exec app bash -c "touch database/database.sqlite && vendor/bin/phpunit"

freshinstall: 
	@echo "AuthServiceProvider fix"
	mv app/Providers/AuthServiceProvider.php app/Providers/AuthServiceProvider.php.original
	mv app/Providers/AuthServiceProvider.php.install app/Providers/AuthServiceProvider.php
	@echo "Fazendo o setup"
	make setup 
	@echo "Fazendo ajustes de permissões"
	make migrateup
	mv app/Providers/AuthServiceProvider.php app/Providers/AuthServiceProvider.php.install
	mv app/Providers/AuthServiceProvider.php.original app/Providers/AuthServiceProvider.php
	docker compose down

	sudo chmod -R 755 ./.docker/postgres/data

	@echo "Subindo contaniers"
	make up
	@echo "Executando NPMs"
	docker compose exec app sh -c 'npm run build'

.PHONY: setup up start down migrateup test freshinstall
