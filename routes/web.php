<?php

use App\Http\Controllers\{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>roll<PERSON>, <PERSON>or<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>roller, <PERSON>latorioController, RegulacaoController};
use Illuminate\Support\Facades\Route;
use Barryvdh\DomPDF\Facade\Pdf;

Route::get('/', [LoginController::class, 'index'])->name('loginForm');
Route::get('/login', [LoginController::class, 'index'])->name('loginForm');
Route::post('/login', [LoginController::class, 'login'])->name('login');
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

Route::group(['middleware' => ['auth']], function () {
   
    Route::resource('home', HomeController::class);
    
    Route::resource('servic<PERSON>', <PERSON><PERSON><PERSON><PERSON><PERSON>roller::class);

    Route::get('/usuario/perfil', [UsuarioController::class,'perfil'])->name('usuario.perfil');
    Route::get('/usuario/listagem', [UsuarioController::class,'index'])->name('usuarios.index');
    Route::get('/usuario/{id}/editar',[UsuarioController::class,'edit'])->name('usuario.edit');
    Route::put('/usuario/{id}', [UsuarioController::class, 'update'])->name('usuario.update');


    Route::get('/{tipoSetor}/listagem', [SetorController::class,'index'])->name('setores.index');
    Route::get('/setor/{tipoSetor}/{id}/editar',[SetorController::class,'edit'])->name('setor.edit');
    Route::put('/setor/{tipoSetor}/{id}', [SetorController::class, 'update'])->name('setor.update');

    Route::get('/chamado', [ChamadoController::class,'index'])->name('chamado.index');
    Route::get('/chamado/novo/{setorAtendenteId}', [ChamadoController::class,'create'])->name('chamado.create');
    Route::post('/chamado/novo', [ChamadoController::class,'store'])->name('chamado.store');
    Route::get('/chamado/{id}', [ChamadoController::class,'show'])->name('chamado.show');
    Route::post('/chamado/{id}/mensagem', [ChamadoController::class,'storeMessage'])->name('chamado.storeMessage');
    Route::post('/chamado/{id}/ingressar', [ChamadoController::class,'ingressar'])->name('chamado.ingressar');
    Route::post('/chamado/{id}/fechamento', [ChamadoController::class,'fechamentoChamado'])->name('chamado.fechamento');
    
    Route::get('/regulacao', [RegulacaoController::class, 'index'])->name('regulacao.index');
    Route::get('/regulacao/{id}/editar', [RegulacaoController::class, 'edit'])->name('regulacao.edit');
    Route::put('/regulacao/{id}', [RegulacaoController::class, 'update'])->name('regulacao.update');


    Route::get('/get/setores/{campusId}', [SetorController::class,'getSetores'])->name('get.setores');
    Route::get('/get/servicos/{setorId}', [ServicoController::class, 'getServicos'])->name('get.servicos');

    Route::prefix('relatorio')->group(function () {
        Route::get('/chamados', [RelatorioController::class, 'relatorioChamados'])->name('relatorio.chamados');
        Route::get('/servicos', [RelatorioController::class, 'relatorioServicos'])->name('relatorio.servicos');
    });
    
    Route::resource('perfil', PerfilController::class);
});
